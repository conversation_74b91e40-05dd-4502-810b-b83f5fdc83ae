/*
* A project specific sketch template code.
* The prefix cu_ is used to prevent conflicts with variables and methods in other files.
* Add the custom _Setup and _Loop method to the DispatchCustomCode.ino file so they are called from the ControlinoIOServer.ino main file.
* Note: don't put any sleep or delay methods in the _Loop method, as this will block the server from running.
*
* A (not exhaustive) list of available methods that are present in the ControlinoIOServer.ino file that might be usefull for custom code:
*   sendTCPAllClients(String message)  // send a message to all connected clients
*   digitalInputLastStableState[pin_idx]  // the last stable state of a digital input pin. The index is the index of the pin in the digitalInputPins array defined in Params.h
*
*  The standard arduino methods, like digitalWrite etc, can also be used of course.
*/

void cu_Setup() {
  // put your setup code here, it is run once when the arduino (re)starts
}

void cu_Loop() {
  // put your main code here, this method will be looped automatically
}
