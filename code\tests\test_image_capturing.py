import asyncio
import os
import unittest
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import numpy as np

from image_capturing import ImageCapturer
from protocols import NewCrateInfo


class TestImageCapturer(unittest.TestCase):
    def setUp(self):
        self.production_event = asyncio.Event()

        # Create a temporary directory for tests
        self.test_dir = Path('/tmp/test_image_capturer')
        self.test_dir.mkdir(exist_ok=True)

        # Initialize the image capturer
        self.image_capturer = ImageCapturer(production_event=self.production_event)

        # Override the base directory for tests
        self.image_capturer._image_dir = self.test_dir

    def tearDown(self):
        # Clean up test directory
        import shutil

        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def test_update_crate_info(self):
        """Test that crate info is correctly stored when received"""
        # Create mock crate info
        test_crate_info = NewCrateInfo(
            telegram_nr='123456',
            trace_id='000000012345',
            timestamp=datetime.now(),
            fill_station='100',
            cell_id='108',
            flight_id='2',
            possible_classes='0000EE00',
        )

        # Initially there should be no crate info
        self.assertIsNone(self.image_capturer._latest_crate_info)

        # Update the crate info
        self.image_capturer._update_crate_info(test_crate_info)

        # Check that the crate info was updated
        self.assertEqual(self.image_capturer._latest_crate_info, test_crate_info)
        assert self.image_capturer._latest_crate_info is not None
        self.assertEqual(self.image_capturer._latest_crate_info.fill_station, '100')
        self.assertEqual(self.image_capturer._latest_crate_info.flight_id, '2')

    @patch('image_capturing.os.path.exists')
    @patch('image_capturing.resize_image')
    @patch('image_capturing.cv2.imwrite')
    @patch('image_capturing.logger')
    @patch('image_capturing.datetime')
    async def test_save_image_with_crate_info(
        self, mock_datetime, mock_logger, mock_imwrite, mock_resize_image, mock_path_exists
    ):
        """Test that image filename includes crate info when available"""
        # Mock dependencies
        mock_path_exists.return_value = True
        mock_resize_image.return_value = np.zeros((100, 100, 3), dtype=np.uint8)
        mock_imwrite.return_value = True

        # Set a fixed datetime for testing
        fixed_datetime = datetime(2025, 4, 25, 12, 34, 56, 789000)
        mock_datetime.now.return_value = fixed_datetime
        mock_datetime.strftime = datetime.strftime

        # Create a dummy image
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)

        # Set up image dir
        self.image_capturer._image_dir = self.test_dir

        # Create mock crate info
        test_crate_info = NewCrateInfo(
            telegram_nr='123456',
            trace_id='000000012345',
            timestamp=datetime.now(),
            fill_station='GS',
            cell_id='108',
            flight_id='2',
            possible_classes='0000EE00',
        )

        # Update crate info
        self.image_capturer._update_crate_info(test_crate_info)

        # Get timestamp for the expected filename
        img_timestamp = fixed_datetime.strftime('%Y-%m-%d_%H-%M-%S.%f')[:-3]

        # Call the function to save an image
        with patch.object(
            self.image_capturer, '_update_image_dir_if_day_has_changed', return_value=Path(self.test_dir)
        ):
            await self.image_capturer._save_image(test_image, f'{img_timestamp}_sizeGS_flight2.png')

        # Check that cv2.imwrite was called with the correct parameters
        expected_path = os.path.join(self.test_dir, f'{img_timestamp}_sizeGS_flight2.png')
        mock_imwrite.assert_called_once()
        self.assertEqual(mock_imwrite.call_args[0][0], expected_path)

    @patch('image_capturing.os.path.exists')
    @patch('image_capturing.cv2.imwrite')
    @patch('image_capturing.resize_image')
    @patch('image_capturing.logger')
    @patch('image_capturing.datetime')
    async def test_gather_images_with_crate_info(
        self, mock_datetime, mock_logger, mock_resize_image, mock_imwrite, mock_path_exists
    ):
        """Test that gather_images_continuously includes crate info in filenames"""
        # Mock dependencies
        mock_path_exists.return_value = True
        mock_resize_image.return_value = np.zeros((100, 100, 3), dtype=np.uint8)
        mock_imwrite.return_value = True

        # Set a fixed datetime for testing
        fixed_datetime = datetime(2025, 4, 25, 12, 34, 56, 789000)
        mock_datetime.now.return_value = fixed_datetime
        mock_datetime.strftime = datetime.strftime

        # Create a mock frame
        mock_frame = MagicMock()
        mock_frame.image = np.zeros((100, 100, 3), dtype=np.uint8)

        # Mock the image queue
        mock_queue = MagicMock()
        mock_queue.get_now.return_value = mock_frame
        mock_queue.is_empty.return_value = True

        # Create mock crate info
        test_crate_info = NewCrateInfo(
            telegram_nr='123456',
            trace_id='000000012345',
            timestamp=datetime.now(),
            fill_station='GS',
            cell_id='108',
            flight_id='2',
            possible_classes='0000EE00',
        )

        # Update crate info
        self.image_capturer._update_crate_info(test_crate_info)

        # Set image dir
        self.image_capturer._image_dir = self.test_dir

        # Set production event to active
        self.production_event.set()

        # Mock the initialize_camera method
        with (
            patch.object(
                self.image_capturer, 'initialize_camera', new=AsyncMock(return_value=(mock_queue, MagicMock()))
            ),
            patch.object(self.image_capturer, '_save_image', new=AsyncMock()) as mock_save_image,
            patch.object(asyncio, 'sleep', new=AsyncMock(side_effect=lambda _: asyncio.CancelledError())),
        ):
            # This will raise CancelledError after first iteration
            with self.assertRaises(asyncio.CancelledError):
                await self.image_capturer.gather_images_continuously()

            # Check that _save_image was called with the correct filename
            expected_filename = f'{fixed_datetime.strftime("%Y-%m-%d_%H-%M-%S.%f")[:-3]}_sizeGS_flight2.png'
            mock_save_image.assert_called_once()
            self.assertEqual(mock_save_image.call_args[0][1], expected_filename)

    @patch('image_capturing.os.path.exists')
    @patch('image_capturing.cv2.imwrite')
    @patch('image_capturing.resize_image')
    @patch('image_capturing.logger')
    @patch('image_capturing.datetime')
    async def test_gather_images_without_crate_info(
        self, mock_datetime, mock_logger, mock_resize_image, mock_imwrite, mock_path_exists
    ):
        """Test that gather_images_continuously uses unknown placeholders when no crate info is available"""
        # Mock dependencies
        mock_path_exists.return_value = True
        mock_resize_image.return_value = np.zeros((100, 100, 3), dtype=np.uint8)
        mock_imwrite.return_value = True

        # Set a fixed datetime for testing
        fixed_datetime = datetime(2025, 4, 25, 12, 34, 56, 789000)
        mock_datetime.now.return_value = fixed_datetime
        mock_datetime.strftime = datetime.strftime

        # Create a mock frame
        mock_frame = MagicMock()
        mock_frame.image = np.zeros((100, 100, 3), dtype=np.uint8)

        # Mock the image queue
        mock_queue = MagicMock()
        mock_queue.get_now.return_value = mock_frame
        mock_queue.is_empty.return_value = True

        # Make sure there is no crate info
        self.image_capturer._latest_crate_info = None

        # Set image dir
        self.image_capturer._image_dir = self.test_dir

        # Set production event to active
        self.production_event.set()

        # Mock the initialize_camera method
        with (
            patch.object(
                self.image_capturer, 'initialize_camera', new=AsyncMock(return_value=(mock_queue, MagicMock()))
            ),
            patch.object(self.image_capturer, '_save_image', new=AsyncMock()) as mock_save_image,
            patch.object(asyncio, 'sleep', new=AsyncMock(side_effect=lambda _: asyncio.CancelledError())),
        ):
            # This will raise CancelledError after first iteration
            with self.assertRaises(asyncio.CancelledError):
                await self.image_capturer.gather_images_continuously()

            # Check that _save_image was called with the correct filename
            expected_filename = f'{fixed_datetime.strftime("%Y-%m-%d_%H-%M-%S.%f")[:-3]}_unknown_unknown.png'
            mock_save_image.assert_called_once()
            self.assertEqual(mock_save_image.call_args[0][1], expected_filename)

    @patch('image_capturing.logger')
    def test_new_crate_subject(self, mock_logger):
        """Test that the new_crate_subject correctly updates crate info when event is received"""
        # Create a mock subscriber to verify the subject works
        mock_callback = MagicMock()

        # Create test crate info
        test_crate_info = NewCrateInfo(
            telegram_nr='123456',
            trace_id='000000012345',
            timestamp=datetime.now(),
            fill_station='GS',
            cell_id='108',
            flight_id='2',
            possible_classes='0000EE00',
        )

        # Subscribe to the subject
        with self.image_capturer.new_crate_subject.subscribe(mock_callback):
            # Push data to the subject
            self.image_capturer.new_crate_subject.on_next(test_crate_info)

        # Check that our callback received the data
        mock_callback.on_next.assert_called_once_with(test_crate_info)

        # Check that the internal callback also received the data and updated the state
        self.assertEqual(self.image_capturer._latest_crate_info, test_crate_info)
        assert self.image_capturer._latest_crate_info is not None
        self.assertEqual(self.image_capturer._latest_crate_info.fill_station, 'GS')
        self.assertEqual(self.image_capturer._latest_crate_info.flight_id, '2')


if __name__ == '__main__':
    unittest.main()
