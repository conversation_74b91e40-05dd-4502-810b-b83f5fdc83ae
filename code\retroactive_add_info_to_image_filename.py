import ast
import glob
import os
import re
import shutil
from datetime import datetime
from pathlib import Path

from loguru import logger

from telegram import Telegram

# Constants
LOG_DIR = '/mnt/sda/logs'
IMAGES_BASE_DIR = '/mnt/sda/images'
LOG_PATTERN = '*_production*.log'
TELEGRAM_PATTERN = r"Received raw telegram from .+?: (b'.*?')"
IMAGE_SAVED_PATTERN = r'Saved image (.+?) to (.+)'
MAX_TIME_DIFF_SECONDS = 2


class PendingTelegramInfo:
    """Class to store pending telegram information"""

    def __init__(self, telegram, timestamp):
        self.fill_station = telegram.new_crate_fill_station
        self.flight_id = telegram.new_crate_flight_id
        self.timestamp = timestamp
        logger.info(
            f'Created pending telegram info: fill_station={self.fill_station}, '
            f'flight_id={self.flight_id}, timestamp={self.timestamp}'
        )


def parse_log_timestamp(log_line):
    """Extract timestamp from log line"""
    try:
        # Example log format: 2025-01-15 12:34:56.789 | INFO | ...
        timestamp_str = log_line.split(' | ')[0]
        return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
    except (ValueError, IndexError):
        return None


def parse_telegram_data(telegram_str):
    """Parse telegram data from log string"""
    try:
        # The telegram_str is a string representation of a Python bytes object
        # Use ast.literal_eval to safely evaluate it back to a bytes object
        telegram_bytes = ast.literal_eval(telegram_str)
        return Telegram.create_from_bytes(telegram_bytes)
    except (ValueError, SyntaxError, TypeError) as e:
        logger.error(f'Error parsing telegram data: {e}')
        return None


def process_logs():
    """Process all production logs and rename images"""
    log_files = sorted(glob.glob(os.path.join(LOG_DIR, LOG_PATTERN)))
    # log_files = [os.path.join(LOG_DIR, "mutshoek_production.2025-03-27_00-00-07_012678.log")]  # ← Do one manually
    logger.info(f'Found {len(log_files)} log files to process')

    # Variable to store the most recent telegram info
    current_telegram_info = None

    for log_file in log_files:
        logger.info(f'Processing log file: {log_file}')

        with open(log_file, 'r', errors='ignore') as f:
            line_num = 0
            for line in f:
                line_num += 1

                timestamp = parse_log_timestamp(line)
                if not timestamp:
                    continue

                # Check for new telegram with cmd 55
                telegram_match = re.search(TELEGRAM_PATTERN, line)
                if telegram_match:
                    telegram_str = telegram_match.group(1)
                    telegram = parse_telegram_data(telegram_str)

                    if telegram and telegram.cmd == 55:
                        logger.info(f'Found cmd 55 telegram at line {line_num}')
                        # Overwrite any previous telegram info
                        current_telegram_info = PendingTelegramInfo(telegram, timestamp)
                    continue

                # Check for saved image
                image_match = re.search(IMAGE_SAVED_PATTERN, line)
                if image_match and current_telegram_info:
                    image_name = image_match.group(1)
                    image_path = image_match.group(2)

                    # Check if the image is within time window of the telegram
                    time_diff = abs((timestamp - current_telegram_info.timestamp).total_seconds())

                    if time_diff <= MAX_TIME_DIFF_SECONDS:
                        logger.info(f'Found matching image for telegram: {image_name} (time diff: {time_diff:.2f}s)')
                        try:
                            rename_image(
                                image_path, current_telegram_info.fill_station, current_telegram_info.flight_id
                            )
                            # Clear the current telegram info as it's been used
                            current_telegram_info = None
                        except Exception as e:
                            logger.error(f'Error renaming image: {e}')
                    elif time_diff > MAX_TIME_DIFF_SECONDS:
                        logger.warning(
                            f'Image timestamp too far from telegram: {time_diff:.2f}s > {MAX_TIME_DIFF_SECONDS}s'
                        )
                        # Clear the current telegram info as it's too old
                        current_telegram_info = None


def rename_image(image_path, fill_station, flight_id):
    """Rename the image file to include fill_station and flight_id"""
    image_path = Path(image_path)

    if not image_path.exists():
        logger.warning(f'Image file not found: {image_path}')
        return

    # Parse the current filename
    filename = image_path.name

    # Check if the image already has size and flight info
    if '_size' in filename and '_flight' in filename:
        logger.debug(f'Image already has size and flight info: {filename}')
        return

    # Get the file extension
    extension = image_path.suffix.lstrip('.')

    # Extract the date_time part - handle filenames with multiple dots
    # For example: 2025-03-05_07-44-51.371.png -> date_time = 2025-03-05_07-44-51.371
    date_time = filename[: -(len(extension) + 1)]

    # Create the new filename
    new_filename = f'{date_time}_size{fill_station}_flight{flight_id}.{extension}'
    new_path = image_path.parent / new_filename

    # Rename the file
    shutil.move(str(image_path), str(new_path))
    logger.info(f'Renamed {image_path.name} to {new_filename}')


def main():
    """Main function"""
    # Configure logger
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        format='<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>',
    )
    # Add file logging
    logger.add(
        f'{LOG_DIR}/retroactive_rename_{{time}}.log',
        rotation='10 MB',
        retention='1 week',
        format='{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}',
    )

    logger.info('Starting retroactive image filename update process')
    process_logs()
    logger.info('Completed retroactive image filename update process')


if __name__ == '__main__':
    import sys

    main()
