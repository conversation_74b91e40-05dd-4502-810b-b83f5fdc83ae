import asyncio
from datetime import datetime, time

from heliovision.communication.controllino.controllino import <PERSON><PERSON>
from heliovision.communication.controllino.pin_layouts import PINS_MAXI_AUTOMATION as PINS
from heliovision.config import config
from loguru import logger

import log_util
from image_capturing import ImageCapturer
from tcp_server import TcpServer
from telegram_handler import TelegramHandler
from watchdog_handler import WatchdogHandler


async def monitor_production(production_event: asyncio.Event):
    """
    Function that monitors the current day and time to determine the production status.

    Args:
        production_event (asyncio.Event): The event to set when production is active.

    Raises:
        ValueError: If the configuration settings are invalid

    """
    check_interval_time_seconds = config.get_setting('schedule', 'check_interval_time_seconds', type=float)
    active_times = config.get_setting('schedule', 'active_times', type=dict)
    active_times = {int(day): times for day, times in active_times.items()}  # Convert keys to integers
    state = None

    if not isinstance(active_times, dict):
        raise ValueError('Active times must be defined as a dictionary.')
    for day, times in active_times.items():
        if not (0 <= day <= 6):
            raise ValueError(f'Invalid weekday: {day}. Must be an integer between 0 and 6.')
        if not isinstance(times, list) or len(times) != 2:
            raise ValueError(f'Invalid times for day {day}: {times}. Must be a list with two elements (start and end).')
        for time_entry in times:
            if not isinstance(time_entry, list) or len(time_entry) != 2:
                raise ValueError(f'Invalid time entry for day {day}: {time_entry}. Must be a list of [hour, minute].')
            hour, minute = time_entry
            if not (0 <= hour < 24 and 0 <= minute < 60):
                raise ValueError(
                    f'Invalid hour/minute values for day {day}: {time_entry}. Hour must be 0-23, minute 0-59.'
                )
        if times[0] >= times[1]:
            raise ValueError(f'Start time must be before end time for day {day}: {times}.')

    while True:
        now = datetime.now()
        current_day = now.weekday()
        current_time = now.time()

        is_active = False
        if current_day in active_times:
            start_hour, start_minute = active_times[current_day][0]
            end_hour, end_minute = active_times[current_day][1]
            start_time = time(start_hour, start_minute)
            end_time = time(end_hour, end_minute)

            if start_time <= current_time < end_time:
                is_active = True

        if is_active:
            if not state:
                production_event.set()
                logger.info('Production has been activated! Inside of production hours.')
                state = True
        else:
            if state or state is None:
                production_event.clear()
                logger.warning('Production has been deactivated! Outside of production hours.')
                state = False

        await asyncio.sleep(check_interval_time_seconds)


async def toggle_lights(production_event: asyncio.Event, lights_relay: Controllino.DigitalOut):
    """
    Function that toggles the lights relay based on the production status.

    Args:
        production_event (asyncio.Event): The event to check if production is active.
        lights_relay (Controllino.DigitalOut): The relay to toggle.

    """
    check_interval_time_seconds = config.get_setting('schedule', 'check_interval_time_seconds', type=float)
    state = None
    while True:
        if production_event.is_set():
            if not state:
                logger.info('Turning on the lights...')
                state = True
                await lights_relay.turn_on()
        else:
            if state or state is None:
                logger.warning('Turning off the lights...')
                state = False
                await lights_relay.turn_off()
        await asyncio.sleep(check_interval_time_seconds)


async def log_sanity_message():
    """
    Function that logs a sanity message to show that the system is still running.
    """
    sanity_message_interval_seconds = config.get_setting('system', 'sanity_message_interval_seconds', type=float)
    while True:
        logger.info('Sanity message: Code still running!')
        await asyncio.sleep(sanity_message_interval_seconds)


async def main():
    """
    This function just is an example function on how code could work
    """
    log_util.logger_init()
    logger.info('############## INITIAL BOOT OF THE SYSTEM ##############')

    reset_wait_time_seconds = config.get_setting('system', 'reset_wait_time_seconds', type=float)
    production_event = asyncio.Event()

    while True:
        task_set = set()
        controllino = None
        tcp_server = None
        try:
            logger.info('############## STARTING THE SYSTEM ##############')
            logger.info('Initializing the system components...')
            image_capturer = ImageCapturer(production_event=production_event)
            tcp_server = TcpServer(
                host=config.get_setting('tcp', 'host', type=str),
                port=config.get_setting('tcp', 'port', type=int),
            )
            telegram_handler = TelegramHandler()
            watchdog_handler = WatchdogHandler()
            tcp_server.incoming_telegram_subject.subscribe(telegram_handler.incoming_telegram_subject)
            telegram_handler.new_watchdog_message_subject.subscribe(watchdog_handler.incoming_telegram_subject)
            telegram_handler.outgoing_telegram_subject.subscribe(tcp_server.outgoing_telegram_subject)
            telegram_handler.new_crate_subject.subscribe(image_capturer.new_crate_subject)
            watchdog_handler.outgoing_telegram.subscribe(tcp_server.outgoing_telegram_subject)

            logger.info('Connecting to the controllino...')
            controllino = Controllino(ip=config.get_setting('controllino', 'ip', type=str))
            controllino.connect_and_start_listening()
            result_conn_controllino = await controllino.wait_for_connection(
                time_limit=config.get_setting('controllino', 'connect_timeout_seconds', type=float)
            )
            if not result_conn_controllino:
                raise ConnectionError('Failed to connect to the controllino.')
            logger.info('Connected to the controllino.')

            lights_relay = Controllino.DigitalOut(controllino=controllino, pin=PINS.RELAY_0, name='Relay_0')

            logger.info('Starting the main tasks ...')
            async with asyncio.TaskGroup() as tg:
                task_sanity_message = tg.create_task(coro=log_sanity_message())
                task_start_tcp_ip_server = tg.create_task(coro=tcp_server.start_server())
                task_monitor_production = tg.create_task(coro=monitor_production(production_event))
                task_toggle_lights = tg.create_task(coro=toggle_lights(production_event, lights_relay))
                task_update_image_dir = tg.create_task(coro=image_capturer.periodic_update_image_dir())
                task_gather_images = tg.create_task(coro=image_capturer.gather_images_continuously())

                task_set.add(task_sanity_message)
                task_set.add(task_start_tcp_ip_server)
                task_set.add(task_monitor_production)
                task_set.add(task_toggle_lights)
                task_set.add(task_update_image_dir)
                task_set.add(task_gather_images)

        except Exception as e:
            logger.exception('An error occurred inside the main code', exception=e)

        finally:
            logger.info('Cancelling all tasks...')
            for task in task_set:
                task.cancel()
            logger.info('All tasks cancelled.')
            if controllino:
                await controllino.disconnect()
                logger.info('Disconnected from the controllino.')
            if tcp_server:
                await tcp_server.stop_server()
                logger.info('TCP/IP server stopped.')
            logger.error(f'Restarting the system in {reset_wait_time_seconds} seconds...')
            await asyncio.sleep(reset_wait_time_seconds)


if __name__ == '__main__':
    asyncio.run(main())
