<script lang="ts">
  // Imports
  import { LabelPage, StartPage } from "./lib";
  import { Spinner } from "@sveltestrap/sveltestrap";
  import { gui } from "./stores.js";
  import { PythonLayout } from "@heliovision/gui";

  const {state} = gui;

  // Reactive variables
  $: isConnected = !$state.connecting;
  let navIndex = 0;
  let userName: string = "";

  $: if ($state.connecting) {
    navIndex = 0;
  }
</script>

<PythonLayout {gui} logo="heliovision" project="Van Asseldonk Mushrooms" xxl={false} fluid={navIndex === 1} md={navIndex === 0}>
  {#if isConnected}
    <!--    <Nav class="custom-nav-bar" tabs={["Folder selectie", "Label pagina"]} index={navIndex} disabled/>-->
    {#if navIndex === 0}
      <StartPage on:start={() => navIndex = 1} bind:userName/>
    {:else if navIndex === 1}
      <LabelPage on:stop={() => navIndex = 0} {userName}/>
    {/if}
  {:else}
    <div class="d-flex align-items-center justify-content-center">
      <Spinner/>
    </div>
  {/if}
</PythonLayout>
