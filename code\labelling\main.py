import asyncio
import os
import sys
from pathlib import Path

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from heliovision.config import config
from labelling_manager import LabellingManager
from loguru import logger

from log_util import logger_init


async def main():
    try:
        logger_init(filename=config.get_setting('logger', 'labelling_filename', type=str))
        logger.info('############ STARTING LABELLING PROCESS ############')
        process = LabellingManager(
            image_parent_folder_path=Path(config.get_setting('camera', 'base_image_dir', type=str)),
            preloaded_range_images=config.get_setting('labelling', 'preloaded_range_images', type=int),
            desired_gui_width_image_pixels=config.get_setting('labelling', 'desired_gui_width_image_pixels', type=int),
            gamma_factor=config.get_setting('labelling', 'gamma_factor', type=float),
            image_extension=config.get_setting('camera', 'image_extension', type=str),
        )
        logger.info('Starting to serve the GUI')
        await process._gui.serve(
            host=config.get_setting('labelling', 'gui_host', type=str),
            port=config.get_setting('labelling', 'gui_port', type=int),
        )

        # block forever
        await asyncio.Future()

    except BaseException as e:
        logger.error(f'Error while running the labelling process: {e!r}')
        raise e
    finally:
        logger.info('Quiting the labelling process...')
        exit(1)


if __name__ == '__main__':
    asyncio.run(main())
