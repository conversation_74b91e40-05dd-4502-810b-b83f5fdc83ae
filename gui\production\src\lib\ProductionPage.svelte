<script lang="ts">
  // Imports
  import { Card } from "@heliovision/sveltestrap";
  import { Col, Row, Spinner } from "@sveltestrap/sveltestrap";
  import { gui } from "../stores.js"

  const {state, liveImages} = gui;
</script>

<style>
    .spinner-placeholder {
        width: 325px;
        height: 325px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>

{#if $state.vision_status && !$state.lights_status}
  <Card title="⚠️ Waarschuwing ⚠️" color="warning-subtle" class="mt-4">
    <h4>Visiesysteem actief, maar de lichten staan uit. G<PERSON><PERSON> de lichten aan te zetten.</h4>
  </Card>
{/if}

<Row>
  <Col style="width: 40%; flex: 1 0 40%;">
    <Card class="mt-4" color="info-subtle" title="Informatie krat">

      <!-- Not how 'Row' and 'Col' are suposed to be used -->
      <Row class="" style="font-size: 25px; font-weight: bold;">
        <Col align="right" class="fw-bold pe-1" style="width: 65%; flex: 1 0 65%;">
          <div>Vlucht:</div>
          <div>Kwaliteitsinstelling lijn:</div>
          <div>Beoordeling:</div>
          <div>Kwaliteitslabel vision:</div>
        </Col>
        <Col align="left" class="ps-1" style="width: 35%; flex: 1 0 35%;">
          <div>{$state.current?.flight || "Onbekend"}</div>
          <div>{$state.current?.quality_preference || "Onbekend"}</div>
          <div>{$state.current?.quality_vision || "Onbekend"}</div>
          <div>{$state.current?.quality_label_vision || "Onbekend"}</div>
        </Col>
      </Row>

    </Card>

  </Col>

  <Col style="width: 60%; flex: 1 0 60%;">
    <Card class="mt-4" color="info-subtle" title="Meest recente foto">

      <Col class="d-flex flex-column align-items-center justify-content-center">
        {#if $liveImages.current_crate}
          <figure class="figure">
            <img
              src={$liveImages.current_crate}
              caption="Champignons"
              class="rounded mx-auto d-block aspect-ratio-1x1"
              style="width: 90%;"
              alt="Champignons"
            />
            <figcaption class="figure-caption"
                        style="margin-top: 5px; font-size: 25px;">{$state.current_crate_timestamp}</figcaption>
          </figure>
        {:else}
          <div class="spinner-placeholder">
            <Spinner/>
          </div>
        {/if}
      </Col>

    </Card>

  </Col>
</Row>
