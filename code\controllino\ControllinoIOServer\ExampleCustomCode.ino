
/*
* A Project specific arduino code example
* The prefix ex_ is used to prevent conflicts with variables and methods in other files 
*/

bool ex_DetectedThreshold = false;  // as a standard, prepend each (global) variable with a unique name to prevent using the same variable names in different files
int ex_ThresholdValue = 350;
const int ex__ANALOG_PIN = 64;

bool ex_Blabla = true;

void ex_Setup() {
  // put your setup code here, it is run once when the arduino (re)starts
  pinMode(ex__ANALOG_PIN, INPUT);
}

void ex_Loop() {
  // put your main code here, this method will be looped automatically

  // Random example code as illustration:
  int val = analogRead(ex__ANALOG_PIN);
  if (val < ex_ThresholdValue) {
    if (!ex_DetectedThreshold) {
      ex_DetectedThreshold = true;
      sendTCPAllClients("CUSTOM_MESSAGE_PROTOCOL_THRESHOLD_VALUE");  // you can call methods defined in other files
    }
  }

  if (digitalInputLastChangeTime[2] > 10 * 60 && ex_Blabla) {  // you can use (global) variables defined in other files (you can update them, but avoid doing this!)
    ex_Blabla = false;
    digitalWrite(ex__ANALOG_PIN, LOW);
  }
}
