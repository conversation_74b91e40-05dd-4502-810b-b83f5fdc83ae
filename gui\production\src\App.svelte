<script lang="ts">
  // Imports
  import { ProductionPage, ConfigurationPage } from "./lib";
  import { Spinner } from "@sveltestrap/sveltestrap";
  import { gui } from "./stores.js";
  import { PythonLayout } from "@heliovision/gui";
  import { Nav } from "@heliovision/sveltestrap";

  const state = gui.state;

  // Reactive variables
  $: isConnected = !$state.connecting;
  let navIndex = 0;
</script>

<PythonLayout project="Van Asseldonk Mushrooms" logo="heliovision" {gui}>
  {#if isConnected}
    <Nav bind:index={navIndex} tabs={["Productie", "Configuratie"]} interactive={true}/>
    {#if navIndex === 0}
      <ProductionPage/>
    {:else}
      <ConfigurationPage/>
    {/if}
  {:else}
    <div class="d-flex align-items-center justify-content-center">
      <h2>Connecting...</h2>
      <Spinner/>
    </div>
  {/if}
</PythonLayout>
