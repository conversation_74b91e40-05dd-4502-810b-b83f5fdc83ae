import unittest

from reactivex import Subject

from telegram import Telegram
from watchdog_handler import WatchdogHandler


class TestWatchdogHandler(unittest.TestCase):
    def setUp(self):
        self.handler = WatchdogHandler()

        self.outgoing_telegrams = []
        self.handler.outgoing_telegram.subscribe(self.outgoing_telegrams.append)

    def test_handle_incoming_watchdog_message_valid(self):
        message = b'\x020PLC101123456_____________________________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(message)
        excepted_response_telegram = Telegram.create_from_bytes(
            b'\x020HVIS02123456_____________________________________________________________________________________\x03'
        )

        self.handler.incoming_telegram_subject.on_next(telegram)

        self.assertEqual(len(self.outgoing_telegrams), 1)
        response = self.outgoing_telegrams[0]
        self.assertEqual(response, excepted_response_telegram)

    def test_handle_incoming_watchdog_message_invalid_command(self):
        message = b'\x020PLC155123456_____________________________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(message)

        self.handler.incoming_telegram_subject.on_next(telegram)

        self.assertEqual(len(self.outgoing_telegrams), 0)

    def test_response_message_extraction(self):
        response_subject = Subject[Telegram]()
        self.handler.outgoing_telegram.subscribe(response_subject.on_next)
        response_messages = []
        response_subject.subscribe(response_messages.append)

        message = b'\x020PLC101123999_____________________________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(message)
        expected_response_telegram = Telegram.create_from_bytes(
            b'\x020HVIS02123999_____________________________________________________________________________________\x03'
        )

        self.handler.incoming_telegram_subject.on_next(telegram)

        self.assertEqual(len(response_messages), 1)
        response = response_messages[0]
        self.assertEqual(response, expected_response_telegram)


if __name__ == '__main__':
    unittest.main()
