import unittest
from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, PropertyMock, patch

from protocols import NewClassification, NewClassificationFeedback, NewCrateInfo
from telegram import Telegram
from telegram_handler import TelegramHandler


class TestTelegramHandler(unittest.TestCase):
    def setUp(self):
        self.handler = TelegramHandler()

    @patch.object(TelegramHandler, '_handle_new_crate')
    @patch.object(TelegramHandler, '_handle_classification_feedback')
    def test_handle_telegram_repeated_flag(self, mock_handle_feedback: MagicMock, mock_handle_new_crate: MagicMock):
        telegram = Telegram()
        telegram.repeat_flag = True
        telegram.sender_id = 'PLC1'
        telegram.cmd = 1
        telegram.telegram_nr = '12345'

        mock_callback = MagicMock()
        with self.handler.new_watchdog_message_subject.subscribe(mock_callback):
            self.handler.handle_telegram(telegram)

        mock_callback.on_next.assert_not_called()
        mock_handle_new_crate.assert_not_called()
        mock_handle_feedback.assert_not_called()

    def test_watchdog_subject(self):
        telegram = Telegram()
        telegram.repeat_flag = False
        telegram.sender_id = 'PLC1'
        telegram.cmd = 1
        telegram.telegram_nr = '123456'

        mock_callback = MagicMock()
        with self.handler.new_watchdog_message_subject.subscribe(mock_callback):
            self.handler.handle_telegram(telegram)

        mock_callback.on_next.assert_called_once_with(telegram)

    @patch.object(TelegramHandler, '_handle_new_crate')
    def test_handle_telegram_new_crate(self, mock_handle_new_crate: MagicMock):
        telegram = Telegram()
        telegram.cmd = 55
        self.handler.handle_telegram(telegram)
        mock_handle_new_crate.assert_called_once_with(telegram)

    @patch.object(TelegramHandler, '_handle_classification_feedback')
    def test_handle_telegram_classification_feedback(self, mock_handle_feedback: MagicMock):
        telegram = Telegram()
        telegram.cmd = 57
        self.handler.handle_telegram(telegram)
        mock_handle_feedback.assert_called_once_with(telegram)

    @patch.object(TelegramHandler, '_handle_new_crate')
    @patch.object(TelegramHandler, '_handle_classification_feedback')
    def test_handle_telegram_unknown_command(self, mock_handle_feedback: MagicMock, mock_handle_new_crate: MagicMock):
        telegram = Telegram()

        with patch.object(type(telegram), 'cmd', new_callable=PropertyMock) as mock_cmd:
            mock_cmd.return_value = 99
            self.assertEqual(telegram.cmd, 99)

            mock_callback = MagicMock()
            with self.handler.new_watchdog_message_subject.subscribe(mock_callback):
                self.handler.handle_telegram(telegram)

        mock_callback.on_next.assert_not_called()
        mock_handle_new_crate.assert_not_called()
        mock_handle_feedback.assert_not_called()

    @patch('telegram_handler.datetime')
    def test_handle_new_crate(self, mock_datetime):
        data = b'\x021PLC155032694000000026351__GS010800020000EE00_____________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        fixed_datetime = datetime(2023, 1, 1, 12, 0, 0)
        mock_datetime.now.return_value = fixed_datetime

        mock_callback = MagicMock()
        with self.handler.new_crate_subject.subscribe(mock_callback):
            self.handler._handle_new_crate(telegram)

        mock_callback.on_next.assert_called_once_with(
            NewCrateInfo(
                telegram_nr=telegram.telegram_nr,
                trace_id=telegram.trace_id,
                timestamp=fixed_datetime,
                fill_station=telegram.new_crate_fill_station,
                cell_id=telegram.new_crate_cell_id,
                flight_id=telegram.new_crate_flight_id,
                possible_classes=telegram.new_crate_possible_classes,
            )
        )

    def test_handle_classification_feedback_valid(self):
        data = b'\x020PLC157032765000000026343___D_1___________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        mock_callback = MagicMock()
        with self.handler.new_classification_feedback_subject.subscribe(mock_callback):
            self.handler._handle_classification_feedback(telegram)

        mock_callback.on_next.assert_called_once_with(
            NewClassificationFeedback(
                trace_id=telegram.trace_id,
                button_pressed=True,
                classification='D',
                manual_classification=True,
            )
        )

    def test_handle_classification_feedback_invalid(self):
        data = b'\x020PLC157032765000000026343___D_0___________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        mock_callback = MagicMock()
        with self.handler.new_classification_feedback_subject.subscribe(mock_callback):
            self.handler._handle_classification_feedback(telegram)

        mock_callback.on_next.assert_not_called()

    def test_handle_classification_no_operator_feedback_case_1(self):
        data = b'\x020PLC157032765000000034843_____0___________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        mock_callback = MagicMock()
        with self.handler.new_classification_feedback_subject.subscribe(mock_callback):
            self.handler._handle_classification_feedback(telegram)

        mock_callback.on_next.assert_called_once_with(
            NewClassificationFeedback(
                trace_id=telegram.trace_id,
                button_pressed=False,
                classification=None,
                manual_classification=None,
            )
        )

    def test_handle_classification_no_operator_feedback_case_2(self):
        data = b'\x020PLC157032765000000034843___0_____________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        mock_callback = MagicMock()
        with self.handler.new_classification_feedback_subject.subscribe(mock_callback):
            self.handler._handle_classification_feedback(telegram)

        mock_callback.on_next.assert_called_once_with(
            NewClassificationFeedback(
                trace_id=telegram.trace_id,
                button_pressed=False,
                classification=None,
                manual_classification=None,
            )
        )

    def test_handle_classification_no_operator_feedback_case_3(self):
        data = b'\x020PLC157032765000000034843_________________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        mock_callback = MagicMock()
        with self.handler.new_classification_feedback_subject.subscribe(mock_callback):
            self.handler._handle_classification_feedback(telegram)

        mock_callback.on_next.assert_called_once_with(
            NewClassificationFeedback(
                trace_id=telegram.trace_id,
                button_pressed=False,
                classification=None,
                manual_classification=None,
            )
        )

    def test_handle_classification_automatic_button_press_feedback(self):
        data = b'\x020PLC157032765000000098343___G_____________________________________________________________________\x03'
        telegram = Telegram.create_from_bytes(data)

        mock_callback = MagicMock()
        with self.handler.new_classification_feedback_subject.subscribe(mock_callback):
            self.handler._handle_classification_feedback(telegram)

        mock_callback.on_next.assert_called_once_with(
            NewClassificationFeedback(
                trace_id=telegram.trace_id,
                button_pressed=True,
                classification='G',
                manual_classification=False,
            )
        )

    def test_send_crate_classification_to_plc(self):
        classification_data = NewClassification(
            telegram_nr='654321',
            trace_id='000987654321',
            classification='F',
        )
        excepted_response = b'\x020HVIS56654321000987654321___F_____________________________________________________________________\x03'
        excepted_response_telegram = Telegram.create_from_bytes(excepted_response)

        mock_callback = MagicMock()
        with self.handler.outgoing_telegram_subject.subscribe(mock_callback):
            self.handler.send_crate_classification_to_plc(classification_data)

        mock_callback.on_next.assert_called_once()
        sent_telegram = mock_callback.on_next.call_args[0][0]

        self.assertEqual(sent_telegram, excepted_response_telegram)

    def test_incoming_classification_for_crate_subject(self):
        mock_callback = MagicMock()
        with self.handler.outgoing_telegram_subject.subscribe(mock_callback):
            classification_data = NewClassification(
                telegram_nr='658821',
                trace_id='010987654321',
                classification='A',
            )
            self.handler.incoming_classification_for_crate_subject.on_next(classification_data)

        excepted_response = b'\x020HVIS56658821010987654321___A_____________________________________________________________________\x03'
        excepted_response_telegram = Telegram.create_from_bytes(excepted_response)

        mock_callback.on_next.assert_called_once()
        sent_telegram = mock_callback.on_next.call_args[0][0]

        self.assertEqual(sent_telegram, excepted_response_telegram)


if __name__ == '__main__':
    unittest.main()
