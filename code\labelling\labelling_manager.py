import asyncio
import os
import re
from pathlib import Path
from typing import Literal

import reactivex as rx
import reactivex.operators as ops
from heliovision.gui import Gui
from labelling_user import UserLabellingSession
from loguru import logger

from utilities import is_valid_image_name


class LabellingManager:
    """
    This class manages the overall labelling process.
    """

    def __init__(
        self,
        image_parent_folder_path: Path,
        preloaded_range_images: int = 10,
        desired_gui_width_image_pixels: int = 1980,
        gamma_factor: float = 1.8,
        image_extension: str = 'png',
    ) -> None:
        self._gui = Gui({'connecting': True})
        self._active_labelling_sessions: dict[str, UserLabellingSession] = {}
        self._valid_image_folders: list[str] = []
        self._preloaded_range_images: int = preloaded_range_images
        self._image_parent_folder_path: Path = image_parent_folder_path
        self._desired_gui_width_image_pixels: int = desired_gui_width_image_pixels
        self._gamma_factor_images: float = gamma_factor
        self._image_extension: str = image_extension

        if not image_parent_folder_path.exists() or not image_parent_folder_path.is_dir():
            raise FileNotFoundError(f'Could not find image parent folder at: {image_parent_folder_path!r}.')

        loop = asyncio.get_event_loop()
        # Update the valid image folders list every hour
        rx.timer(0, 3600).pipe(
            ops.do_action(lambda x: self._update_valid_image_folders_list()),
            ops.do_action(lambda x: loop.call_soon_threadsafe(self._gui.broadcast_state) and None),
        ).subscribe()

        self._gui.state = self
        self._gui.register_multi_command_handler(self)

    def __json__(self) -> dict:
        return {
            'folders': sorted(self._valid_image_folders),
            'users': self._active_labelling_sessions,
        }

    def _is_user_labeling(self, user_name: str) -> bool:
        return user_name.strip().lower() in self._active_labelling_sessions

    def _update_valid_image_folders_list(self) -> None:
        """
        Update the list of valid image folders inside the parent image folder.
        """
        logger.info(f'Updating valid image folders list for the parent folder at: {self._image_parent_folder_path!r}.')
        self._valid_image_folders.clear()
        folder_contents = os.scandir(self._image_parent_folder_path)

        def is_valid_root_folder(folder: os.DirEntry[str]) -> bool:
            return not folder.name.startswith('.') and folder.is_dir()

        potential_image_folders = filter(is_valid_root_folder, folder_contents)

        for folder in potential_image_folders:
            folder_path = os.path.join(self._image_parent_folder_path, folder.name)
            for i in os.scandir(folder_path):
                if is_valid_image_name(i.name, self._image_extension):
                    self._valid_image_folders.append(str(folder.name))
                    break

        if not self._valid_image_folders:
            logger.error(
                f'No valid image folders found inside the parent folder at: {self._image_parent_folder_path!r}.'
            )
        else:
            logger.info(f'Valid image folders found inside the parent image folder: {self._valid_image_folders}')

    def _handle_start_labelling(self, user_name: str, input_folder: str) -> None:
        """
        Handle a request to start labelling for a specific user.

        Args:
            user_name: The name of the user to start labelling.
            input_folder: The name of the folder containing the images to label.
        """
        user_name = user_name.strip().lower()
        if not user_name or not re.match(r'^[a-zA-Z _-]+$', user_name):
            logger.error(
                f'Invalid user_name: {user_name!r} It cannot be None or an empty string and can only contain letters.'
            )
            self._gui.show_message(
                message=f'Ongeldige gebruikersnaam: {user_name!r}. Deze mag niet leeg zijn en kan alleen letters bevatten.',
                level='error',
                time_out=10,
            )
            return

        input_folder_path: str = os.path.join(self._image_parent_folder_path, input_folder).replace('\\', '/')
        if not os.path.exists(input_folder_path) or not os.path.isdir(input_folder_path):
            logger.error(f'Could not find folder at: {input_folder_path!r}.')
            return

        logger.info(f'User: {user_name!r} wants to start labelling the folder at: {input_folder!r}.')
        if self._is_user_labeling(user_name):
            logger.warning(f'User: {user_name!r} is already labelling. Killing previous labelling session.')
            self._active_labelling_sessions[user_name].handle_stop_labelling()
            del self._active_labelling_sessions[user_name]

        self._active_labelling_sessions[user_name] = UserLabellingSession(
            gui=self._gui,
            name=user_name,
            folder_path=input_folder_path,
            image_extension=self._image_extension,
            preload_range_images=self._preloaded_range_images,
            gamma_factor=self._gamma_factor_images,
            desired_gui_width_image_pixels=self._desired_gui_width_image_pixels,
        )
        self._gui.broadcast_state()
        return

    def _handle_stop_labelling(self, user_name: str) -> None:
        """
        Handle a request to stop labelling for a specific user.

        Args:
            user_name: The name of the user to stop labelling.
        """
        if not self._is_user_labeling(user_name):
            logger.error(f'User: {user_name!r} is not currently labelling. Cannot stop labelling.')
            return
        self._active_labelling_sessions[user_name].handle_stop_labelling()
        del self._active_labelling_sessions[user_name]

    def _handle_save_labelling(self, user_name: str, timestamp: float) -> None:
        """
        Handle a request to save the labelling for a specific user.

        Args:
            user_name: The name of the user to save the labelling for.
            timestamp: The timestamp of the save request
        """
        if not self._is_user_labeling(user_name):
            logger.error(f'User: {user_name!r} is not currently labelling. Cannot save labelling.')
            return
        self._active_labelling_sessions[user_name].handle_save_labelling(timestamp)

    def _handle_switch_image(
        self,
        user_name: str,
        action: Literal['previous', 'first', 'next', 'last', 'first-unlabelled', 'index'],
        index: int | None = None,
    ) -> None:
        """
        Handle a request to switch the image for a specific user.

        Args:
            user_name: The name of the user to switch the image for.
            action: The action to perform. One of: 'previous', 'first', 'next', 'last', 'first-unlabelled', 'index'.
        """
        if not self._is_user_labeling(user_name):
            logger.error(f'User: {user_name!r} is not currently labelling. Cannot switch image.')
            return
        self._active_labelling_sessions[user_name].handle_switch_image(action, index)

    def _handle_button(
        self, user_name: str, button: Literal['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'Run-off'] | None
    ) -> None:
        """
        Handle a button push for a specific user.
        If the button is None, the label was cleared.

        Args:
            user_name: The name of the user to handle the button push for.
            button: The button that was pushed by the user. One of: 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'Run-off', None.
        """
        if not self._is_user_labeling(user_name):
            logger.error(f'User: {user_name!r} is not currently labelling. Cannot push a button then.')
            return
        self._active_labelling_sessions[user_name].handle_button(button)
