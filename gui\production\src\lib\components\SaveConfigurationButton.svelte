<script lang="ts">
    // Imports
    import { gui } from "../../stores.js";
    import { <PERSON><PERSON>, ModalHeader, ModalFooter } from "@sveltestrap/sveltestrap";
    import { But<PERSON> } from "@heliovision/sveltestrap";

    export let selectedConfig: string | null = null;
    export let sortingHeaders: string[] = [];
    export let sortingData: string[][] = [];

    let isModalOpen = false;
    let newConfigName = '';

    function toggleModal() {
        isModalOpen = !isModalOpen;
        newConfigName = ''; // Reset input field when modal is closed
    }

    function saveConfiguration() {
        gui.command("save_configuration", [selectedConfig, sortingHeaders, sortingData]);
        toggleModal();
    }
</script>

<!-- Button to open the modal -->
<Button 
    color="success" 
    on:click={toggleModal}
    style="font-size: 1.4em; padding-top: 4px;"
    >
    Opslaan
</Button>

<!-- Modal for entering the new configuration name -->
<Modal isOpen={isModalOpen} toggle={toggleModal}>
    <ModalHeader toggle={toggleModal}>
        Huidige configuratie opslaan?
    </ModalHeader>
    <ModalFooter>
        <Button color="success" on:click={saveConfiguration}>
            Opslaan
        </Button>
        <Button color="secondary" on:click={toggleModal}>
            Cancel
        </Button>
    </ModalFooter>
</Modal>

