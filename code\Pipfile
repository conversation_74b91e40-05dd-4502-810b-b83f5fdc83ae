[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[[source]]
url = "https://__token__:${HV_PYPI_TOKEN}@git.heliovision.be/api/v4/groups/32/-/packages/pypi/simple"
verify_ssl = true
name = "hv-pypi"

[packages]
loguru = ">=0.7.2"
numpy = ">=1.26.4"
opencv-python = "==4.10.*, >=4.10.0.84"
reactivex = "==4.*, >=4.0.4"
"heliovision.communication.controllino" = {version = "==2.2.*, >=2.2.1", index = "hv-pypi"}
"heliovision.camera.alkeria" = {version = "==1.2.*, >=1.2.0", index = "hv-pypi"}
"heliovision.camera.base" = {version = "==0.7.*, >=0.7.1", index = "hv-pypi"}
"heliovision.config-system" = {version = "==0.8.*, >=0.8.0", index = "hv-pypi"}
"heliovision.gui" = {version = "==3.5.0a1, >=3.4.0", index = "hv-pypi"}
"alkusb3" = {version = ">=1.8", index = "hv-pypi", markers = "sys_platform == 'win32'"}
matplotlib = "*"

[dev-packages]
ruff = "*"
pyright = "*"
rclone-python = "*"

[scripts]
tests = "python -m unittest"

[requires]
python_version = "3.11"
