import unittest

from telegram import Telegram


class TestTelegram(unittest.TestCase):
    def setUp(self):
        self.telegram = Telegram()
        self.default_bytes = Telegram._Telegram__DEFAULT_BYTES  # type: ignore

    def test_initial_bytes(self):
        self.assertEqual(self.telegram.to_bytes(), self.default_bytes)

    def test_set_and_get_repeat_flag(self):
        self.telegram.repeat_flag = True
        self.assertTrue(self.telegram.repeat_flag)

        self.telegram.repeat_flag = False
        self.assertFalse(self.telegram.repeat_flag)

    def test_set_and_get_sender_id(self):
        sender_id = 'HVIS'
        self.telegram.sender_id = sender_id
        self.assertEqual(self.telegram.sender_id, sender_id)

    def test_set_sender_id_invalid_length(self):
        with self.assertRaises(ValueError):
            self.telegram.sender_id = 'TOOLONG'

    def test_set_and_get_cmd(self):
        self.telegram.cmd = 55
        self.assertEqual(self.telegram.cmd, 55)

    def test_set_cmd_invalid_value(self):
        with self.assertRaises(ValueError):
            self.telegram.cmd = 99

    def test_set_and_get_telegram_nr(self):
        self.telegram.telegram_nr = '123456'
        self.assertEqual(self.telegram.telegram_nr, '123456')

    def test_set_and_get_telegram_nr_smaller_string(self):
        self.telegram.telegram_nr = '456'
        self.assertEqual(self.telegram.telegram_nr, '000456')

    def test_set_telegram_nr_invalid_value_too_long(self):
        with self.assertRaises(ValueError):
            self.telegram.telegram_nr = '1234567'

    def test_set_telegram_nr_invalid_value_out_of_range(self):
        with self.assertRaises(ValueError):
            self.telegram.telegram_nr = '0'

    def test_set_and_get_trace_id(self):
        trace_id = '000123456789'
        self.telegram.trace_id = trace_id
        self.assertEqual(self.telegram.trace_id, trace_id)

    def test_set_trace_id_invalid_length(self):
        with self.assertRaises(ValueError):
            self.telegram.trace_id = '12'

    def test_set_and_get_ai_inference_classification(self):
        classification = 'A'
        self.telegram.ai_inference_classification = classification
        self.assertEqual(self.telegram.ai_inference_classification, classification)

    def test_set_ai_inference_classification_invalid_values(self):
        with self.assertRaises(ValueError):
            self.telegram.ai_inference_classification = 'INVALID'

        with self.assertRaises(ValueError):
            self.telegram.ai_inference_classification = 'Z'

    def test_set_telegram_part_invalid_length(self):
        with self.assertRaises(ValueError):
            self.telegram._set_telegram_part(index=slice(6, 8), value=b'12345')

    def test_create_from_bytes(self):
        data = b'\x021PLC155031609000000026223__6001080002000DEE00_____________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.to_bytes(), data)

    def test_create_from_bytes_invalid_length(self):
        data = (
            b'\x020PLC101015451___________________________________________________________________________________\x03'
        )
        with self.assertRaises(ValueError):
            Telegram.create_from_bytes(data)

    def test_equality(self):
        telegram_a = Telegram()
        telegram_b = Telegram()
        self.assertEqual(telegram_a, telegram_b)

        telegram_a.sender_id = 'TEST'
        self.assertNotEqual(telegram_a, telegram_b)

    def test_str_representation(self):
        self.telegram = Telegram()
        self.assertEqual(str(self.telegram), repr(self.default_bytes))

    def test_check_properties_create_from_bytes_new_crate_telegram(self):
        data = b'\x021PLC155031609000000026223__6001080002000DEE00_____________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'PLC1')
        self.assertEqual(new_telegram.repeat_flag, True)
        self.assertEqual(new_telegram.cmd, 55)
        self.assertEqual(new_telegram.telegram_nr, '031609')
        self.assertEqual(new_telegram.trace_id, '000000026223')
        self.assertEqual(new_telegram.new_crate_fill_station, '60')
        self.assertEqual(new_telegram.new_crate_cell_id, '108')
        self.assertEqual(new_telegram.new_crate_flight_id, '2')
        self.assertEqual(new_telegram.new_crate_possible_classes, '000DEE00')

    def test_check_properties_create_from_bytes_new_crate_telegram_KS_case(self):
        data = b'\x020PLC155031631000000026241__KS0108000200000E00_____________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'PLC1')
        self.assertEqual(new_telegram.repeat_flag, False)
        self.assertEqual(new_telegram.cmd, 55)
        self.assertEqual(new_telegram.telegram_nr, '031631')
        self.assertEqual(new_telegram.trace_id, '000000026241')
        self.assertEqual(new_telegram.new_crate_fill_station, 'KS')
        self.assertEqual(new_telegram.new_crate_cell_id, '108')
        self.assertEqual(new_telegram.new_crate_flight_id, '2')
        self.assertEqual(new_telegram.new_crate_possible_classes, '00000E00')

    def test_check_properties_create_from_bytes_new_crate_telegram_GS_case(self):
        data = b'\x021PLC155032694000000026351__GS010800020000EE00_____________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'PLC1')
        self.assertEqual(new_telegram.repeat_flag, True)
        self.assertEqual(new_telegram.cmd, 55)
        self.assertEqual(new_telegram.telegram_nr, '032694')
        self.assertEqual(new_telegram.trace_id, '000000026351')
        self.assertEqual(new_telegram.new_crate_fill_station, 'GS')
        self.assertEqual(new_telegram.new_crate_cell_id, '108')
        self.assertEqual(new_telegram.new_crate_flight_id, '2')
        self.assertEqual(new_telegram.new_crate_possible_classes, '0000EE00')

    def test_check_properties_create_from_bytes_classification_feedback_telegram(self):
        data = b'\x020PLC157032765000000026343___D_1___________________________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'PLC1')
        self.assertEqual(new_telegram.repeat_flag, False)
        self.assertEqual(new_telegram.cmd, 57)
        self.assertEqual(new_telegram.telegram_nr, '032765')
        self.assertEqual(new_telegram.trace_id, '000000026343')
        self.assertEqual(new_telegram.classification_feedback_button_pressed, 'D_1')

    def test_check_properties_create_from_bytes_classification_feedback_telegram_case_empty_feedback(self):
        data = b'\x020PLC157008209000000027256_________________________________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'PLC1')
        self.assertEqual(new_telegram.repeat_flag, False)
        self.assertEqual(new_telegram.cmd, 57)
        self.assertEqual(new_telegram.telegram_nr, '008209')
        self.assertEqual(new_telegram.trace_id, '000000027256')
        self.assertEqual(new_telegram.classification_feedback_button_pressed, '')

    def test_check_properties_create_from_bytes_ai_classification_feedback_telegram(self):
        data = b'\x020HVIS56008219000010027256___A_____________________________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'HVIS')
        self.assertEqual(new_telegram.repeat_flag, False)
        self.assertEqual(new_telegram.cmd, 56)
        self.assertEqual(new_telegram.telegram_nr, '008219')
        self.assertEqual(new_telegram.trace_id, '000010027256')
        self.assertEqual(new_telegram.ai_inference_classification, 'A')

    def test_check_properties_watchdog_telegram_receiver(self):
        data = b'\x020PLC101018280_____________________________________________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'PLC1')
        self.assertEqual(new_telegram.repeat_flag, False)
        self.assertEqual(new_telegram.cmd, 1)
        self.assertEqual(new_telegram.telegram_nr, '018280')
        self.assertEqual(new_telegram.trace_id, '')
        self.assertEqual(new_telegram.new_crate_fill_station, '')
        self.assertEqual(new_telegram.new_crate_cell_id, '')
        self.assertEqual(new_telegram.new_crate_flight_id, '')
        self.assertEqual(new_telegram.new_crate_possible_classes, '')
        self.assertEqual(new_telegram.classification_feedback_button_pressed, '')
        self.assertEqual(new_telegram.ai_inference_classification, '')

    def test_check_properties_watchdog_telegram_sender(self):
        data = b'\x020HVIS02018278_____________________________________________________________________________________\x03'
        new_telegram = Telegram.create_from_bytes(data)
        self.assertEqual(new_telegram.sender_id, 'HVIS')
        self.assertEqual(new_telegram.repeat_flag, False)
        self.assertEqual(new_telegram.cmd, 2)
        self.assertEqual(new_telegram.telegram_nr, '018278')
        self.assertEqual(new_telegram.trace_id, '')
        self.assertEqual(new_telegram.new_crate_fill_station, '')
        self.assertEqual(new_telegram.new_crate_cell_id, '')
        self.assertEqual(new_telegram.new_crate_flight_id, '')
        self.assertEqual(new_telegram.new_crate_possible_classes, '')
        self.assertEqual(new_telegram.classification_feedback_button_pressed, '')
        self.assertEqual(new_telegram.ai_inference_classification, '')


if __name__ == '__main__':
    unittest.main()
