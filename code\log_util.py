import os
import sys
from typing import Optional

from heliovision.config import config
from loguru import logger


def logger_init(filename: Optional[str] = None) -> None:
    log_dir = config.get_setting('logger', 'dir', type=str)
    if filename is None:
        filename = config.get_setting('logger', 'filename', type=str)

    if not os.path.exists(log_dir):
        raise FileNotFoundError(f'Could not find log directory at {log_dir}.')

    log_file_name_default = os.path.join(log_dir, filename).replace('\\', '/')

    logger.remove(0)
    logger.add(
        sink=sys.stdout,
        level=config.get_setting('logger', 'level', type=str),
        diagnose=True,
    )
    logger.add(
        sink=log_file_name_default,
        level=config.get_setting('logger', 'level', type=str),
        rotation=config.get_setting('logger', 'rotation', type=str),
        retention=config.get_setting('logger', 'retention', type=str),
        compression=None,
        diagnose=True,
    )
