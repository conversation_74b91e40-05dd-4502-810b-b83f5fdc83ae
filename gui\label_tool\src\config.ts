export const debug = new URLSearchParams(window.location.search).get("mode") == "debug";
const host = new URLSearchParams(window.location.search).get("host") || window.location.hostname;
const port = new URLSearchParams(window.location.search).get("port") || 8769;

// Web Socket Store Config
export const websocketConfig = {
  host: host,
  port: +port,
};

export type UserState = {
  current: {
    index: number;
    flight: number;
    format: string;
    label: string | null;
    crateImageName: string;
    imageId: string;
  };
  folder: string;
  totalAmountOfImages: number;
  lastSavedTimestamp: number;
}

export type State = {
  connecting?: boolean;
  folders: string[];
  users: Record<string, UserState>;
}

export const debugData: State = {
  connecting: false,
  folders: [
    '2025-01-10',
    '2025-01-11',
    '2025-01-15',
  ],
  users: {
    'robbe': {
      folder: '2025-01-11',
      current: {
        index: 69,
        flight: 1,
        format: "Massief jongue",
        label: "F",
        crateImageName: "Onbekend",
        imageId: '$$not-set$$',
      },
      totalAmountOfImages: 420,
      lastSavedTimestamp: 0,
    }
  }
}

export const defaultUserState: UserState = {
  current: {
    index: 0,
    flight: 0,
    format: "Onbekend",
    label: "Onbekend",
    crateImageName: "Onbekend",
    imageId: '$$not-set$$',
  },
  folder: '',
  totalAmountOfImages: 0,
  lastSavedTimestamp: 0,
}

export const POSSIBLE_BUTTONS = ["A", "B", "C", "D", "E", "F", "G", "H", "Run-off"];
