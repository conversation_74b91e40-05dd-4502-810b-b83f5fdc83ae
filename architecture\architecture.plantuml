@startuml MIKE

'skin rose

title Van Asseldonk Mushrooms Architecture
' left to right direction
hide empty members
skinparam nodesep 8
skinparam ranksep 5


class FrontmatecComm
class Main
class Camera
class Gui
interface ClassifierProtocol

class AIClassifier extends ClassifierProtocol
class FakeClassifier extends ClassifierProtocol

FrontmatecComm -(0- Main: handle crate
FrontmatecComm -(0- Main: > set operator result
Main -(0- Camera: < get image
Main -(0- ClassifierProtocol: classify
Main -(0- Gui: > show image and data

Main --0)- Gui: < handle system on/off

@enduml