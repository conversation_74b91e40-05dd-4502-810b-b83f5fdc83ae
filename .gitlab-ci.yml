stages:
  - lint
  - test

variables:
  PIPENV_VENV_IN_PROJECT: 1
  WORKING_DIRECTORY: $CI_PROJECT_DIR/code
  CACHE_DIRECTORY: $CI_PROJECT_DIR/code/.cache
  PIP_CACHE_DIR: $CACHE_DIRECTORY/pip
  NPM_CACHE_DIR: $CACHE_DIRECTORY/npm_cache
  CI_FILE: $CI_PROJECT_DIR/.gitlab-ci.yml

ruff-lint:
  stage: lint
  image: python:3.11
  before_script:
    - echo $CI_FILE
    - ls
    - cat $CI_FILE

    - cd $WORKING_DIRECTORY

    # Install pipenv and dependencies
    - python --version
    - python -m pip install pipenv
    - echo $HV_PYPI_TOKEN
    - pipenv sync --dev --verbose
  script:
    - pipenv run ruff check .
    - pipenv run ruff check . > ruff_report.txt
    - echo "Done with Ruff type check"
  artifacts:
    paths:
      - $WORKING_DIRECTORY/ruff_report.txt
  rules:
    - when: never  # TEMP DISABLE CAUSE BROKEN
    # Don't run on merge requests
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - changes:
        - .gitlab-ci.yml
        - "**/*.py"
        - $WORKING_DIRECTORY/Pipfile
        - $WORKING_DIRECTORY/Pipfile.lock
        - $CI_FILE
    - exists:
        - $WORKING_DIRECTORY/Pipfile
  cache:
    - key:
        files:
          - $WORKING_DIRECTORY/Pipfile.lock
          - $CI_FILE
      paths:
      - $WORKING_DIRECTORY/.ruff_cache
      - $PIP_CACHE_DIR
      - $WORKING_DIRECTORY/.venv

pyright-check:
  stage: lint
  image: python:3.11  # Start with the Python base image
  before_script:
    - cd $WORKING_DIRECTORY

    - apt update && apt install -y curl
    # Install Node.js (LTS version)
    - curl -fsSL https://deb.nodesource.com/setup_22.x | bash -
    - apt install -y nodejs
    - node --version
    - npm --version

    # Install pipenv and dependencies
    - python --version
    - python -m pip install pipenv
    - echo $HV_PYPI_TOKEN
    - pipenv sync --dev --verbose

    # Install pyright
    - mkdir -p $NPM_CACHE_DIR
    - npm config set cache $NPM_CACHE_DIR
    - npm install -g pyright
  script:
    - pipenv run pyright
    - pipenv run pyright > pyright_report.txt
  artifacts:
    paths:
      - $WORKING_DIRECTORY/pyright_report.txt
  rules:
    - when: never  # TEMP DISABLE CAUSE BROKEN
    # Don't run on merge requests
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - changes:
        - .gitlab-ci.yml
        - "**/*.py"
        - $WORKING_DIRECTORY/Pipfile
        - $WORKING_DIRECTORY/Pipfile.lock
        - $CI_FILE
    - exists:
        - $WORKING_DIRECTORY/Pipfile
  cache:
    - key:
        files:
          - $WORKING_DIRECTORY/package-lock.json
      paths:
        - $CACHE_DIRECTORY/.npm
        - $CI_FILE
    - key:
        files:
          - $WORKING_DIRECTORY/Pipfile.lock
      paths:
        - $PIP_CACHE_DIR
        - $WORKING_DIRECTORY/.venv
        - $CI_FILE

test:
  stage: test
  image: python:3.11
  before_script:
    - cd $WORKING_DIRECTORY
    - python --version
    - python -m pip install pipenv
    - echo $HV_PYPI_TOKEN
    - pipenv sync --dev
  script:
    - pipenv run tests > tests_report.txt
    - echo "Done with tests"
  artifacts:
    paths:
      - $WORKING_DIRECTORY/tests_report.txt
  rules:
    - when: never  # TEMP DISABLE CAUSE BROKEN
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - changes:
        - .gitlab-ci.yml
        - "**/*.py"
        - $WORKING_DIRECTORY/Pipfile.lock
    - exists:
        - $WORKING_DIRECTORY/Pipfile
  cache:
    - key:
        files:
          - $WORKING_DIRECTORY/Pipfile.lock
      paths:
        - $PIP_CACHE_DIR
        - $WORKING_DIRECTORY/.venv
