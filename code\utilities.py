import json
import os
import re
from datetime import datetime
from glob import glob
from pathlib import Path
from typing import Optional

import cv2 as cv
import numpy as np
from loguru import logger


def get_timestamp(format='%Y-%m-%d_%H-%M-%S-%f'):
    """
    Gets a string of the current timestamp in the given format. Default: year-month-day_hour-minute-second-fractionalpart.
    """
    str_date_time = datetime.now().strftime(format)
    return str_date_time


def load_json(json_file: Path) -> dict:
    if not json_file.is_file():
        logger.error(f'Cannot read json as file does not exist! {json_file}')
        raise Exception(f'Cannot read json as file does not exist! {json_file}')

    try:
        with open(json_file) as f:
            json_data = json.load(f)
        return json_data
    except Exception as e:
        logger.error(f'File cannot be read as json! {json_file}')
        raise Exception(f'File cannot be read as json! {json_file}') from e


def write_json(file_path: Path, json_data: dict):
    folder = file_path.parent

    folder.mkdir(parents=True, exist_ok=True)

    if file_path.exists():
        logger.debug(f'Overwriting content in file {file_path}')

    try:
        with open(file_path, 'w') as f:
            json.dump(json_data, f, indent=2)
    except Exception:
        logger.error(f'Could not write json data! {file_path}: {json_data}')
        raise Exception(f'Could not write json data! {file_path}: {json_data}')


def is_valid_image_name(image_name: str, image_extension: str = 'png') -> bool:
    """
    This method checks if the image name complies with the naming convention.
    The naming convention is: YYYY-MM-DD_HH-MM-SS.mmm_sizeXX_flightN.png
    where XX is 2-3 characters (letters or digits) and N is exactly 1 digit.

    Args:
        image_name (str): The name of the image.
        image_extension (str): The image extension to check for. Default is 'png'.

    Returns:
        bool: True if the image name complies with the naming convention, False otherwise.
    """
    pattern = re.compile(
        r'^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.\d{3}_size[A-Za-z0-9]{2,3}_flight\d\.' + image_extension + r'$'
    )
    return bool(pattern.match(image_name))


def get_all_images_inside_folder(path: str, image_extension: str = 'png') -> list[str]:
    """
    This method gets a list of all images (with the desired extension) in a folder that comply with the naming convention.

    Args:
        path (str): The path to the folder.
        image_extension (str): The image extension to search for. Default is 'png'.

    Returns:
        list[str]: A sorted list of all image names in the folder that match the naming convention.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f'Could not find image folder at {path}')

    image_path_list = glob(os.path.join(path, f'*{image_extension}'))
    image_names = {os.path.basename(image_path) for image_path in image_path_list}

    filtered_images = {image_name for image_name in image_names if is_valid_image_name(image_name, image_extension)}
    image_list = sorted(filtered_images)

    return image_list


def gamma_correct_image(image: np.ndarray, gamma: float = 1.8) -> np.ndarray:
    """
    This method applies gamma correction to an image.

    Args:
        image (np.ndarray): The image to apply gamma correction to.
        gamma (float): The gamma value to apply. Default is 1.8.

    Returns:
        np.ndarray: The gamma corrected image.
    """
    if gamma <= 0.0:
        raise ValueError('Gamma value must be greater than 0')
    if gamma >= 5.0:
        raise ValueError('Gamma value must be less than 5')
    if gamma == 1.0:
        return image

    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype('uint8')

    return cv.LUT(image, table)


def resize_image(
    image: np.ndarray, desired_width_pixels: Optional[int] = None, desired_height_pixels: Optional[int] = None
) -> np.ndarray:
    """
    This method resizes an image to the desired image width or height in pixels.
    The aspect ratio is maintained.

    Args:
        image (np.ndarray): The image to resize.
        desired_width_pixels (int): The desired width of the image in pixels.
        desired_height_pixels (int): The desired height of the image in pixels

    Returns:
        np.ndarray: The resized image.

    Raises:
        ValueError: If both desired_width and desired_height are None or if both are not None.
    """
    if (desired_width_pixels is None and desired_height_pixels is None) or (
        desired_width_pixels and desired_height_pixels
    ):
        raise ValueError("You must specify either 'desired_width' or 'desired_height', but not both.")

    original_height, original_width = image.shape[:2]
    new_dimension = desired_width_pixels if desired_width_pixels else desired_height_pixels
    is_width = desired_width_pixels is not None

    if not new_dimension:
        raise ValueError('New dimension is not specified.')

    scale_ratio = new_dimension / (original_width if is_width else original_height)
    new_width, new_height = int(original_width * scale_ratio), int(original_height * scale_ratio)

    resized_image = cv.resize(image, (new_width, new_height), interpolation=cv.INTER_AREA)
    return resized_image
