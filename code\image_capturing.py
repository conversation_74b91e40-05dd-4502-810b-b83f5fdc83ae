import asyncio
import os
from datetime import datetime
from pathlib import Path
from typing import Optional

import cv2
import numpy as np
from heliovision.camera.alkeria import AlkeriaCamera
from heliovision.camera.base import FrameQueue
from heliovision.config import config
from loguru import logger
from reactivex import Subject

from protocols import NewCrateInfo
from utilities import resize_image


class ImageCapturer:
    def __init__(self, production_event: asyncio.Event):
        logger.debug('Initializing ImageCapturer class ...')
        self._production_event = production_event
        self._last_checked_date = None
        self._image_dir = None
        self._base_dir = config.get_setting('camera', 'base_image_dir', type=str)
        self._camera_settings_file_path = config.get_setting('camera', 'camera_settings_file_path', type=str)
        self._camera_type = config.get_setting('camera', 'camera_type', type=str)
        self._camera_index = config.get_setting('camera', 'camera_index', type=int)
        self._should_save_images = config.get_setting('camera', 'should_save_images', type=bool)
        self._image_extension = config.get_setting('camera', 'image_extension', type=str)
        self._check_image_dir_interval_seconds = config.get_setting(
            'camera', 'check_image_dir_interval_seconds', type=float
        )
        self._sleep_time_production_active_seconds = config.get_setting(
            'camera', 'sleep_time_production_active_seconds', type=float
        )
        self._sleep_time_production_inactive_seconds = config.get_setting(
            'camera', 'sleep_time_production_inactive_seconds', type=float
        )
        self._desired_height_image_pixels = config.get_setting('camera', 'desired_height_image_pixels', type=int)

        # Store the latest crate information
        self._latest_crate_info: Optional[NewCrateInfo] = None
        # Create a subject to subscribe to new crate events
        self.new_crate_subject = Subject[NewCrateInfo]()
        # Subscribe to our own subject to update the latest crate info
        self.new_crate_subject.subscribe(self._update_crate_info)

        if not os.path.exists(self._base_dir):
            raise FileNotFoundError(f'Could not find base directory at {self._base_dir}')
        if not os.path.exists(self._camera_settings_file_path):
            raise FileNotFoundError(f'Could not find camera settings file at {self._camera_settings_file_path}')

    def _update_crate_info(self, crate_info: NewCrateInfo) -> None:
        """
        Update the latest crate information.

        Args:
            crate_info (NewCrateInfo): The new crate information
        """
        logger.info(f'Updating latest crate info: cell_id={crate_info.cell_id}, flight_id={crate_info.flight_id}')
        self._latest_crate_info = crate_info

    def _update_image_dir_if_day_has_changed(self) -> Path:
        """
        This function checks if the current date has changed. If it has,
        it updates the image dir to reflect the new date and returns it.
        If not, it returns the current image directory.

        Returns:
            Path: The (new) image directory.

        """
        logger.info('Checking if we should make a new image directory...')
        current_date = datetime.now().strftime('%Y-%m-%d')

        if self._last_checked_date == current_date and self._image_dir is not None:
            return Path(self._image_dir)

        logger.info(f'Day has changed from {self._last_checked_date} to {current_date}. Updating image directory...')
        new_image_dir = os.path.join(self._base_dir, current_date)
        if not os.path.exists(new_image_dir):
            os.makedirs(new_image_dir)
        self._last_checked_date = current_date
        logger.info(f'Updated image directory to {new_image_dir}!')

        return Path(new_image_dir)

    async def _save_image(self, image: np.ndarray, image_name: str) -> None:
        """
        This function saves the image to the image directory.
        If the image directory does not exist, the method is called to create it.

        Args:
            image (np.ndarray): The image to save.
            image_name (str): The name of the image.

        Raises:
            FileNotFoundError: If the image directory does not exist.
        """
        if self._image_dir is None:
            self._image_dir = self._update_image_dir_if_day_has_changed()
        if not os.path.exists(self._image_dir):
            raise FileNotFoundError(f'Could not find image directory at {self._image_dir}')

        image_path = os.path.join(self._image_dir, image_name)
        resized_image = resize_image(image, desired_height_pixels=self._desired_height_image_pixels)
        cv2.imwrite(image_path, resized_image)
        logger.info(f'Saved image {image_name} to {image_path}')

    async def periodic_update_image_dir(self) -> None:
        """
        Periodically update the image directory if the day has changed.
        """
        if not self._should_save_images:
            return

        while True:
            self._image_dir = self._update_image_dir_if_day_has_changed()
            await asyncio.sleep(self._check_image_dir_interval_seconds)

    async def initialize_camera(self) -> tuple[FrameQueue, AlkeriaCamera]:
        """
        This function initializes the camera. It loads the configuration file and starts the camera.
        The ImageQueue and the camera object are returned.

        Returns:
            tuple[FrameQueue, AlkeriaCamera]: A tuple containing the image queue and the camera object.

        Raises:
            FileNotFoundError: If the configuration file is not found.
        """
        camera = AlkeriaCamera(camera_type=self._camera_type, index=self._camera_index)
        logger.info('Trying to connect to the camera...')
        await camera.connect()
        logger.info('Connected to the camera successfully!')
        camera.load_configuration(self._camera_settings_file_path)
        image_queue = camera.start_run()
        logger.info('Camera started successfully.')

        return image_queue, camera

    async def gather_images_continuously(self) -> None:
        """
        This function gathers images from the camera continuously.
        It only looks for images when the production event is set.
        Saving images is optional and can be set in the configuration file.
        """
        logger.info('############## Starting to gather images ##############')
        image_queue, _ = await self.initialize_camera()

        while True:
            if not self._production_event.is_set():
                logger.info('Outside production. Sleeping...')
                while not image_queue.is_empty():
                    try:
                        current_frame = image_queue.get_now()
                        logger.info(
                            'Retrieved an image from the buffer outside of production hours. It will be discarded.'
                        )
                    except asyncio.QueueEmpty:
                        # To cover the edge case where an image might appear between the is_empty check and the get_now call
                        break
                try:
                    await asyncio.wait_for(
                        self._production_event.wait(), timeout=self._sleep_time_production_inactive_seconds
                    )
                except asyncio.TimeoutError:
                    pass
            else:
                try:
                    current_frame = image_queue.get_now()
                    img_timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S.%f')[:-3]
                    logger.info(f'Retrieved image with timestamp: {img_timestamp}')

                    if not current_frame:
                        logger.warning('Frame retrieved from camera was None.')
                        continue

                    if self._should_save_images:
                        # Include size (cell_id) and flight information in filename if available
                        if self._latest_crate_info:
                            img_name = f'{img_timestamp}_size{self._latest_crate_info.fill_station}_flight{self._latest_crate_info.flight_id}.{self._image_extension}'
                            self._latest_crate_info = None  # Only use the latest crate info once
                        else:
                            img_name = f'{img_timestamp}.{self._image_extension}'
                            logger.warning('No crate information available.')

                        await self._save_image(current_frame.image, img_name)

                except asyncio.QueueEmpty:
                    pass

                finally:
                    await asyncio.sleep(self._sleep_time_production_active_seconds)
