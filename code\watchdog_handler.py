from typing import Optional

import reactivex.operators as ops
from loguru import logger
from reactivex import Subject

from telegram import Telegram


class WatchdogHandler:
    def __init__(self) -> None:
        """
        Initialize the watchdog handler with a callback for sending responses to the PLC.
        """
        self.incoming_telegram_subject = Subject[Telegram]()
        self.outgoing_telegram = self.incoming_telegram_subject.pipe(
            ops.map(self._handle_incoming_watchdog_message), ops.filter(lambda x: isinstance(x, Telegram))
        )

    def _handle_incoming_watchdog_message(self, telegram: Telegram) -> Optional[Telegram]:
        """
        Handle an incoming watchdog message from the PLC.
        If the telegram command is not 1, the telegram is ignored and None is returned.

        Args:
            telegram: The received telegram to handle. Must be a Telegram instance.

        Returns:
            The response telegram to send to the PLC, or None if the telegram was ignored.
        """
        if telegram.cmd != 1:
            logger.warning(
                f'Expected a watchdog message, but received a different command: {telegram.cmd!r}. Ignoring this telegram ({str(telegram)!r}).'
            )
            return

        logger.info(f'Received a new watchdog message from sender {telegram.sender_id!r}.')
        response = Telegram()
        response.repeat_flag = telegram.repeat_flag
        response.sender_id = 'HVIS'
        response.cmd = 2
        response.telegram_nr = telegram.telegram_nr
        logger.info(f'Created watchdog response Telegram for the PLC: {str(response)!r}')
        return response
