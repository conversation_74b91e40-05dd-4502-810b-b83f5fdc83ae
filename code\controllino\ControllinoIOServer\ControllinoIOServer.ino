#include <SPI.h>
#include <Ethernet.h>
#include <EEPROM.h>
#include "Params.h"

// Increase this version number when new features or changes were made
const String SKETCH_VERSION_NUMBER = "0.1.1"; 
const String KEYWORD_SKETCH_VERSION_NUMBER = "SKETCH_VERSION_NUMBER";

const char compile_date[] = __DATE__ " " __TIME__;

// Struct holding all relevant config params that need to be stored persistent
struct EEPROM_DATA {
  IPAddress ip;  // IP address for the Ethernet shield
  byte mac[6];   // MAC address for your Ethernet shield}
  // type controllino
};
const int START_ADDRESS_MY_EEPROM_DATA = sizeof(byte);
const byte START_SEQUENCE_EEPROM = 152;  // A pre-defined 'sequence of bits' that is stored at the start of the EEPROM to check if we already savved config params on this controllino or not
EEPROM_DATA myEEPROMData;

// Template for config params. These are filled in by serial commands when EEPROM was not yet set
EEPROM_DATA defaultData = {
  IPAddress(1, 1, 1, 1),
  { 0xDE, 0xAD, 0xBE, 0xEF, 0x00, 0x00 }
};

const int SERVERPORT = 6321;
EthernetServer server(SERVERPORT);

// Global variables for listen_for_serial
String serialReceivedMsg = "";
const String KEYWORD_NEW_IPADDRESS = "UPDATE_IP_ADDRESS=";
const int LENGTH_KEYWORD_NEW_IP_ADDRESS = KEYWORD_NEW_IPADDRESS.length();  // returns the length without trailing null character

// Global variables for listen_to_clients
String tcpReceivedMsg = "";


int digitalInputNewState[NB_DIGITAL_INPUT_PINS];                                           // gets the 'current' value
int digitalInputLastStableState[NB_DIGITAL_INPUT_PINS];                                    // stores the last stable state (debounced)
unsigned long digitalInputLastChangeTime[NB_DIGITAL_INPUT_PINS];                           // stores the millis() when an input has changed most recently
const int DEBOUNCE_TIME = 5;                                                             // in milliseconds
unsigned long currentMillis;

// Set next line to true for serial output debugging. Note: This will cause a lot of latency!
bool printToSerial = false;

// TCP-IP client connection variables
const int MAX_NB_CLIENTS = 2;
EthernetClient tcpClients[MAX_NB_CLIENTS];


void setup() {
  // Open serial communications and wait for port to open:
  Serial.begin(9600);

  //flash_leds();  // funny demo of the outputs/on-board leds giving a visual clue the Controllino has started
  readEEPROM();
  setupEthernetShield();
  setupPorts();

  dispatchCustomCodeSetup(); // call all the 'setup' methods of custom-code files

  // start listening for clients
  server.begin();
}


void loop() {
  listenForSerial();
  monitorInputs();

  dispatchCustomCodeLoop(); //call all the 'loop' methods of custom-code files

  checkServerConnections();
}

void mySerialPrint(String msg) {
  if (printToSerial) {
    Serial.print(msg);
  }
}

void mySerialPrintln(String msg) {
  if (printToSerial) {
    Serial.println(msg);
  }
}

void listenForSerial() {
  if (Serial.available() > 0) {  // Check for data available
    serialReceivedMsg = Serial.readStringUntil('\n');
    serialReceivedMsg.trim();  // Remove any \r \n whitespace at the end of the String
    Serial.println("Got your message, will do what is needed!");

    // Note: switch-case statement doesn't work with strings
    if (serialReceivedMsg == "PRINT_SERIAL_ENABLED") {
      printToSerial = true;
      Serial.println("print_to_serial is set to true!");
    } else if (serialReceivedMsg == "PRINT_SERIAL_DISABLED") {
      printToSerial = false;
      Serial.println("print_to_serial is set to false!");
    } else if (serialReceivedMsg.startsWith(KEYWORD_NEW_IPADDRESS) > 0) {
      //my_eeprom_data.ip = IPAddress(192, 168, 12, 21);
      String new_ip = serialReceivedMsg.substring(LENGTH_KEYWORD_NEW_IP_ADDRESS);
      new_ip.trim();
      Serial.println(">" + new_ip + "<");
      myEEPROMData.ip.fromString(new_ip);  //Note: new IP must be a (C-) string with only numbers and dots, no end-line characters!

      Serial.print("New ip address is: ");
      Serial.println(myEEPROMData.ip);
      EEPROM.put(START_ADDRESS_MY_EEPROM_DATA, myEEPROMData);
      Serial.println("IP address updated, restart device to use these new settings (unplug and replug the USB cable for example)");
    } else if (serialReceivedMsg == "GET_EEPROM") {
      byte current_start_sequence_eeprom = EEPROM.read(0);
      EEPROM_DATA current_eeprom_data;
      EEPROM.get(START_ADDRESS_MY_EEPROM_DATA, current_eeprom_data);

      Serial.print("current_start_sequence_eeprom: ");
      Serial.println(current_start_sequence_eeprom);
      //
      Serial.print("current IP address: ");
      Serial.println(current_eeprom_data.ip);
      //
      Serial.print("current mac address: ");
      for (int i = 0; i < 6; i++) {
        Serial.print(current_eeprom_data.mac[i]);
      }
      Serial.println("");
    }
    else if (serialReceivedMsg == KEYWORD_SKETCH_VERSION_NUMBER){
      Serial.print("SKETCH_VERSION_NUMBER=");
      Serial.println(SKETCH_VERSION_NUMBER);
    }
    else if (serialReceivedMsg == "SKETCH_INFO"){
      Serial.println("FILENAME=" + String(__FILE__));
      Serial.println("TIME_UPLOAD=" + String(__TIME__));
      Serial.println("DATE_UPLOAD=" + String(__DATE__));
    } else {
      Serial.print("Got unknown command: ");
      Serial.println(serialReceivedMsg);
    }
  }
}

byte ask_hex(String index_info){
  String msg;
  char msgBuffer[5];
  Serial.println("Give a " + index_info + " random hex number (0-9 A-F) for the mac address (between 00-FF). Examples are: 5F, 32, 2A, E3 or 42");

  while (!Serial.available());
  if (Serial.available()){
    msg = Serial.readStringUntil('\n');
    msg.toCharArray(msgBuffer, 5);
  
    long converted_msg = strtol(msgBuffer, NULL, 16);
    byte hex_byte = (byte) converted_msg;
    Serial.print("Got: ");
    Serial.println(hex_byte, HEX);
    return hex_byte;
  }
}

String ask_ip(){
  Serial.println("Give a new ip address (like *************)");
  while (!Serial.available());
  String new_ip = Serial.readStringUntil('\n');
  new_ip.trim();
  Serial.println("new_ip: >" + new_ip + "<");
  return new_ip;
}

void flash_leds() {
  const int nb_output_pins = 12;
  // Note: the output pins don't need the 'D' (eg D5) compared to the inputs (eg A11)
  const int output_pins[nb_output_pins] = { 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13 };

  // Set all digital output pins to output
  for (int i = 0; i < nb_output_pins; i++) {
    pinMode(output_pins[i], OUTPUT);
  }

  for (int i = 0; i < nb_output_pins; i++) {
    digitalWrite(output_pins[i], HIGH);
    delay(50);
  }
  delay(1000);
  for (int i = nb_output_pins - 1; i >= 0; i--) {
    if (i % 2 == 0)
      digitalWrite(output_pins[i], LOW);
    else
      digitalWrite(output_pins[12 - i], LOW);
    delay(50);
  }
}

void readEEPROM() {
  //-------------------------------------------------------------
  // If controllino was configured before, load all the settings
  // else, ask for them

  int start_address_eeprom = 0;
  // read a byte at the given address in the EEPROM
  byte start_sequence_in_eeprom = EEPROM.read(start_address_eeprom);
  if (start_sequence_in_eeprom == START_SEQUENCE_EEPROM) {
    Serial.println("Found our start sequence in EEPROM");
    EEPROM.get(START_ADDRESS_MY_EEPROM_DATA, myEEPROMData);
    Serial.println("All parameters form EEPROM are loaded (but without garantee of correctness)");
  } else {
    Serial.println("");
    Serial.println("!!!!!!!!!!!!!!!!!!!!! ATTENTION !!!!!!!!!!!!!!!!!!!");
    Serial.println("EEPROM was not yet initialised with config parameters. Provide the neccesarry parameters now");
    byte nb1 = ask_hex("first");
    byte nb2 = ask_hex("second");
    defaultData.mac[4] = nb1;
    defaultData.mac[5] = nb2;

    String new_ip = ask_ip();
    defaultData.ip.fromString(new_ip);  //Note: new IP must be a (C-) string with only numbers and dots, no end-line characters!

    EEPROM.put(0, START_SEQUENCE_EEPROM);
    EEPROM.put(START_ADDRESS_MY_EEPROM_DATA, defaultData);

    EEPROM.get(START_ADDRESS_MY_EEPROM_DATA, myEEPROMData);  // 'setupEthernetShield' expects to have the data in 'myEEPROMData'
    Serial.println("  -----  Initialisation Completed, main program will start ----");
  }
}

void setupEthernetShield() {
  // Start the Ethernet connection and the server
  Ethernet.begin(myEEPROMData.mac, myEEPROMData.ip);
}

void setupPorts() {
  // Configure the outputs as OUTPUT ports and the input as INPUT ports
  // example output: pinMode(PIN_NB, OUTPUT);

  /*
    NOTE (for normal Aruino's):
      When you do digitalWrite(pin, HIGH) to a pin that's in INPUT mode it turns on 
      the internal pull-up resistor, which could appear as if you had 
      the pin in OUTPUT mode and had set it HIGH.

      The pullup resistors are controlled by the same registers (internal chip memory locations) 
      that control whether a pin is HIGH or LOW. Consequently, a pin that is configured to have 
      pullup resistors turned on when the pin is an INPUT, will have the pin configured as HIGH 
      if the pin is then switched to an OUTPUT with pinMode(). 
      This works in the other direction as well, and an output pin that is left in a HIGH state 
      will have the pullup resistors set if switched to an input with pinMode().
  */

  for (int i = 0; i <= NB_DIGITAL_INPUT_PINS - 1; i++) {
    pinMode(digitalInputPins[i], INPUT);
    //pinMode(AnalogInputPins[i], INPUT_PULLUP);  // With Controllino, you can't use arduino's internal input_pullup resistor
  }

  // Store states at startup of all inputs
  for (int i = 0; i <= NB_DIGITAL_INPUT_PINS - 1; i++) {
    int result = digitalRead(digitalInputPins[i]);
    digitalInputNewState[i] = result;
    digitalInputLastStableState[i] = result;
  }
}

void checkServerConnections() {
  // check for any new client connecting, and say hello (before any incoming data)
  EthernetClient newClient = server.accept();
  bool accepcted = false;
  if (newClient) {
    for (int i = 0; i < MAX_NB_CLIENTS; i++) {
      if (!tcpClients[i]) {
        mySerialPrint("We have a new client #");
        mySerialPrintln(String(i));
        newClient.print("Hello, client number: ");
        newClient.println(i);
        // Once we "accept", the client is no longer tracked by EthernetServer
        // so we must store it into our list of clients
        tcpClients[i] = newClient;
        accepcted = true;
        break;
      }
    }
    if (!accepcted) {
      mySerialPrintln("Hello, the maximum number of clients is already reached. Closing the connection, goodby!");
      newClient.println("Hello, the maximum number of clients is already reached. Closing the connection, goodby!");
      newClient.stop();
    }
  }

  // // check for incoming data from all clients
  // for (byte i=0; i < MAX_NB_CLIENTS; i++) {
  //   if (clients[i] && clients[i].available() > 0) {
  //     // read bytes from a client
  //     byte buffer[80];
  //     int count = clients[i].read(buffer, 80);
  //     // write the bytes to all other connected clients
  //     for (int j=0; j < MAX_NB_CLIENTS; j++) {
  //       if (j != i && clients[j].connected()) {
  //         clients[j].write(buffer, count);
  //       }
  //     }
  //   }
  // }


  for (int i = 0; i < MAX_NB_CLIENTS; i++) {
    if (tcpClients[i]) {
      if (tcpClients[i].connected()) {
        listenForMessages(tcpClients[i]);
      } else {
        // stop any clients which disconnect
        mySerialPrint("A client disconnected #");
        mySerialPrintln(String(i));
        tcpClients[i].stop();
      }
    }
  }
}

void monitorInputs() {
  for (int i = 0; i <= NB_DIGITAL_INPUT_PINS - 1; i++) {
    int result = digitalRead(digitalInputPins[i]);
    currentMillis = millis();
    if (result != digitalInputNewState[i]) {
      // the state of the pin is different than the most recent not-debounced state for this pin
      // aka, a possible fast new state occurred
      digitalInputLastChangeTime[i] = currentMillis;
      // debug:
      Serial.print("Input changed ");
      Serial.println(digitalInputPins[i]);
    }

    if ((currentMillis - digitalInputLastChangeTime[i]) > DEBOUNCE_TIME) {
      // The last time this pin changed was a 'long' time ago, thus proceeding
      if (result != digitalInputLastStableState[i]) {
        // Only if the new reading is different than the last stable state, we consider it a the new stable state
        digitalInputLastStableState[i] = result;
        sendDetectedInputUpdate(digitalInputPins[i], ToInputValue(result), currentMillis);
      }
    }

    // Update the most recent not-debounced state for this pin
    digitalInputNewState[i] = result;
  }
}

int ToInputValue(int value) {
  // For regular INPUT pins, the logic is as is
  // For INPUT_PULLUP pins, the logic has to be reversed
  return value;
}

void sendDetectedInputUpdate(int pin, int value, unsigned long current_millis) {
  // current_millis can be used to send extra info
  String msg = "UPD_" + String(pin) + "_" + value;
  mySerialPrint("Sent: ");
  mySerialPrintln(msg);
  sendTCPAllClients(msg);
}

void listenForMessages(EthernetClient client) {
  if (client.available()) {
    char c = client.read();
    if (c == '\n') {
      // Newline character indicated full msg was received
      handleTCPMessage(client, tcpReceivedMsg);
      tcpReceivedMsg = "";

    } else if (c != '\r') {
      tcpReceivedMsg += c;
    }
  }
}

void handleTCPMessage(EthernetClient client, String request) {
  //mySerialPrint("got: ");
  //mySerialPrintln(request);

  if (request.startsWith(KEYWORD_SKETCH_VERSION_NUMBER)){
    String msg = KEYWORD_SKETCH_VERSION_NUMBER + "=" + SKETCH_VERSION_NUMBER;
    client.println(msg);
    return;
  }

  if (request.startsWith("REQ_ALL")){
    for (int i=0; i<NB_DIGITAL_INPUT_PINS; i++){
      sendUpdatePin(client, digitalInputPins[i], digitalInputLastStableState[i]);
    }
    return;
  }

  const int length_cmd_keyword = 3;
  String cmdKeyword = request.substring(0, 0 + length_cmd_keyword); // end index is exclusive

  // Possible messages see 'TCP protocol.md'
  int indexPortType = length_cmd_keyword+1;  //+1 because underscore at end of cmd_keyword
  int indexPortNb = indexPortType+2;       //+1 because of PortType letter, and again +1 for the underscore at end of port type

  String portType = request.substring(indexPortType, indexPortType+1);
  String pinNbStr = request.substring(indexPortNb, indexPortNb + 2);   // Assumption: port nb always 2 digits!
  int pinNb = pinNbStr.toInt();
  int value = -1;

  // Handle read_input requests
  if (cmdKeyword == "REQ"){
    if (portType == "D") { 
      // TODO verify that the provided pinNb is a analog/digital input port (output also ok?)
      value = digitalRead(pinNb);
    }
    else if (portType  == "A"){
      value = analogRead(pinNb);
    }
    else{
      // Invalid command received
      invalid_command_received(client, request);
    }
    sendUpdatePin(client, pinNb, value);
  }
  else if (cmdKeyword == "SET"){
    int indexNewValue = indexPortNb + 3; // Assume port nb with 2 digits and an underscore
    String newValueStr = request.substring(indexNewValue);
    int newValue = newValueStr.toInt();

    // SET_D_{pin}_{value}  with value 0 or 1, or SET_A_{pin}_{value} with value 0-255 for PWM or AnalogOutput (0-10v)
    if (portType == "D"){ 
      // TODO verify that the provided pinNb is a analog/digital input port (output also ok?)
      digitalWrite(pinNb, newValue);
    }
    else if (portType  == "A"){
      analogWrite(pinNb, newValue);
    }
    else{
      // Invalid command received
      invalid_command_received(client, request);
    }
    // TODO if pinNb < 9: prepend a '0' to make it 2 digit?
    String response = "DBG_" + String(pinNb) + "_" + String(newValue); 
    sendTCP(client, response);
  }
}

void sendUpdatePin(EthernetClient client, int pinNb, int value){
  String response = "UPD_" + String(pinNb) + "_" + String(value);
  sendTCP(client, response);
}

void invalid_command_received(EthernetClient client, String request){
  String response = "Invalid command received: >" + request + "<";
  sendTCP(client, response);
}


// Utility function to extract the pin index from the request URL
int getPinIndex(String request, String prefix) {
  int prefixLen = prefix.length();
  int start = request.indexOf(prefix) + prefixLen;
  //int end = request.indexOf(' ', start);
  if (start > 0) {                             //} && end > start) {
    String pinStr = request.substring(start);  //, end);
    int pinIndex = pinStr.toInt();
    return pinIndex;
  }
  return -1;  // Return -1 if pin index not found
}

// Utility function to send HTTP responses
int sendResponseBody(String responseBody, EthernetClient client) {
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: application/json");
  client.println("Content-Length: " + String(responseBody.length()));
  // client.println("Connection: close");
  client.println();  // Empty line is important as it indicates the start of the body
  client.print(responseBody);
}

void sendTCP(EthernetClient client, String msg) {
  if (client.connected()) {
    client.println(msg);
    //mySerialPrint("Send msg to client:" );
    //mySerialPrintln(msg);
  } else {
    //mySerialPrintln("Client not connected, not sending message");
  }
}

void sendTCPAllClients(String msg) {
  for (int i = 0; i < MAX_NB_CLIENTS; i++) {
    sendTCP(tcpClients[i], msg);
  }
}
