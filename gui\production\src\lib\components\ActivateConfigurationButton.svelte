<script lang="ts">
    // Imports
    import { gui } from "../../stores.js";
    import { <PERSON><PERSON>, <PERSON><PERSON>Header, ModalFooter } from "@sveltestrap/sveltestrap";
    import { Button } from "@heliovision/sveltestrap";

    export let selectedConfig: string | null = null;
    export let sortingHeadersShown: string[] = [];
    export let sortingDataShown: string[][] = [];
    export let sortingHeaders: string[] = [];
    export let sortingData: string[][] = [];

    let isModalOpen = false;
    let newConfigName = '';

    function toggleModal() {
        isModalOpen = !isModalOpen;
        newConfigName = ''; // Reset input field when modal is closed
    }

    function activateConfiguration() {
        if (!checkIfConfigurationIsValid()) {
            return;
        }
        gui.command("activate_configuration", [selectedConfig]);
        toggleModal();
    }

    function areArraysEqual(array1: unknown[], array2: unknown[]) {
        return JSON.stringify(array1) === JSON.stringify(array2);
    }

    function checkIfConfigurationIsValid() {
        if (!areArraysEqual(sortingHeadersShown, sortingHeaders) || !areArraysEqual(sortingDataShown, sortingData)) {
            alert('De configuratie is nog niet opgeslagen. De configuratie is niet geactiveerd.');
            return false;
        }
        return true;
    }
</script>

<!-- Button to open the modal -->
<Button 
    color="info" 
    on:click={toggleModal}
    style="font-size: 1.4em; padding-top: 4px;"
    >
    Activeren
</Button>

<!-- Modal for entering the new configuration name -->
<Modal isOpen={isModalOpen} toggle={toggleModal}>
    <ModalHeader toggle={toggleModal}>
        Huidige configuratie activeren?
    </ModalHeader>
    <ModalFooter>
        <Button color="success" on:click={activateConfiguration}>
            Activeer
        </Button>
        <Button color="secondary" on:click={toggleModal}>
            Cancel
        </Button>
    </ModalFooter>
</Modal>

