<script lang="ts">
    import { gui } from "../../stores.js";
    
    export let selectedConfig: string | null = null;
    export let configFiles: string[] = [];

    function changeConfig(value: string) {
        gui.command("change_configuration", [value,]);
    }
</script>

<select value={selectedConfig} on:change={(value) => changeConfig(value.target.value)}>
    {#if configFiles}
        {#each configFiles as config}
            <option value={config}>{config}</option>
        {/each}
    {/if}
</select>

<style>
    select {
        width: 100%;
        padding: 0.5em;
        font-size: 1.4em;
        border: 0px;
        border-radius: 10px;
        background-color: white;
    }

    option {
        padding: 0px;
        margin: 0px;
    }
</style>
