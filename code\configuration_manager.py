import functools
import os
from pathlib import Path
from typing import Callable, Concatenate, ParamSpec

from heliovision.config import config
from heliovision.gui import Gui
from loguru import logger

from utilities import get_timestamp, load_json, write_json

PLACEHOLDER_CONFIGURATION_DATA = {
    'sorting_headers': ['Maat/Sorteringen', 'A', 'B', 'C', 'D', 'E'],
    'sorting_data': [['', '', '', '', '', ''] for _ in range(10)],
}

_P = ParamSpec('_P')


def wrap_handler(
    func: 'Callable[Concatenate[ConfigurationManager, _P], None]',
) -> 'Callable[Concatenate[ConfigurationManager, _P], None]':
    @functools.wraps(func)
    def wrapper(self: 'ConfigurationManager', *args: _P.args, **kwargs: _P.kwargs) -> None:
        try:
            func(self, *args, **kwargs)
        except Exception as e:
            logger.exception('Error while handling command')
            self.gui.show_message(f'Er is een fout opgetreden: {e}', 'error', time_out=7.5)

        self.gui.broadcast_state()

    return wrapper


class ConfigurationManager:
    def __init__(self, gui: Gui):
        # Get paths
        self.sorting_configuration_folder: Path = config.get_setting(
            'sorting_configuration', 'configuration_file_folder', type=Path
        )
        self.configuration_backups_folder: Path = config.get_setting(
            'sorting_configuration', 'configuration_backups_folder', type=Path
        )
        self.last_configuration_pointer: Path = config.get_setting(
            'sorting_configuration', 'last_configuration_pointer', type=Path
        )
        self.active_quality_mapping = {}

        self.config_files = []
        self.active_config: str | None = None
        self.selected_config: str | None = None
        self.configuration_data: dict | None = None

        if not os.path.exists(self.sorting_configuration_folder):
            logger.warning(f'Could not find sorting configuration folder at {self.sorting_configuration_folder}')
        if not os.path.exists(self.configuration_backups_folder):
            logger.warning(f'Could not find configuration backups folder at {self.configuration_backups_folder}')
        if not os.path.exists(self.last_configuration_pointer):
            logger.warning(f'Could not find last configuration pointer at {self.last_configuration_pointer}')

        self.gui = gui
        self.init()

    def __json__(self):
        return {
            'active_config': self.active_config,
            'config_files': [i.stem for i in self.config_files],
            'selected_config': self.selected_config,
            'configuration_data': self.configuration_data,
        }

    def init(self):
        # Load list of configuration files
        self.load_config_files()

        # Find last configuration
        last_configuration_path = None
        try:
            last_configuration_path = Path(load_json(self.last_configuration_pointer)['path'])
            logger.info(f'{last_configuration_path} was last configuration ')
        except Exception:
            pass

        # If the file does not exist, use the first configuration
        if (last_configuration_path is None or last_configuration_path not in self.config_files) and len(
            self.config_files
        ) > 0:
            last_configuration_path = self.config_files[0]
            logger.warning(f'Using {last_configuration_path} as last configuration because no last one was found')

        if last_configuration_path is None:
            logger.warning('Could not find any config!!')
            self.selected_config = 'NONE FOUND'
            self.configuration_data = PLACEHOLDER_CONFIGURATION_DATA
            self.active_config = self.selected_config
            self.update_code()
            self.gui.broadcast_state()
            return

        # Show in GUI
        self.selected_config = last_configuration_path.stem
        self.configuration_data = load_json(last_configuration_path)

        self.active_config = self.selected_config

        # Update code
        self.update_code()
        self.gui.broadcast_state()

    def load_config_files(self):
        # Load list of configuration files
        self.config_files = list(self.sorting_configuration_folder.glob('*.json'))
        self.config_files.sort(key=lambda x: x.stem)
        config_files_names = [file.stem for file in self.config_files]
        logger.debug(f'Found {len(self.config_files)} configuration files: {config_files_names}')
        self.gui.broadcast_state()

    ### Functions for sorting configuration
    def update_code(self):
        # Expose the mapping to the code
        quality_labels = self.configuration_data['sorting_headers']
        sorting_rows = self.configuration_data['sorting_data']

        # Fill out empty cells with the closest data
        new_sorting_rows = []
        for row in sorting_rows:
            new_sorting_row = row[1:]
            new_sorting_row = fill_empty_with_left(new_sorting_row[::-1])[::-1]
            new_sorting_row = fill_empty_with_left(new_sorting_row)
            new_sorting_row = [
                row[0],
            ] + new_sorting_row
            new_sorting_rows.append(new_sorting_row)
        sorting_rows = new_sorting_rows

        # Convert the lists to a dictionary mapping
        quality_mapping = {}
        for row in sorting_rows:
            quality_preference = row[0]
            row_mapping = {}
            for i in range(1, len(row)):
                quality_mapped = row[i]
                quality_label = quality_labels[i]
                row_mapping[quality_label] = quality_mapped
            quality_mapping[quality_preference] = row_mapping

        # Save in active_quality_mapping
        self.active_quality_mapping = quality_mapping

    @wrap_handler
    def handle_activate_configuration(self, config_name: str):
        # Update the GUI
        self.active_config = config_name

        # Update the code
        self.configuration_data = load_json(self.sorting_configuration_folder / f'{config_name}.json')
        self.update_code()

        # Update the last configuration pointer
        write_json(
            self.last_configuration_pointer,
            {'path': str(self.sorting_configuration_folder / f'{config_name}.json')},
        )

    @wrap_handler
    def handle_change_configuration(self, config_name: str):
        # Get the new configuration
        self.selected_config = config_name
        self.configuration_data = load_json(self.sorting_configuration_folder / f'{config_name}.json')

    @wrap_handler
    def handle_create_configuration(self, config_name: str):
        # Load new placeholder configuration
        self.configuration_data = PLACEHOLDER_CONFIGURATION_DATA
        self.selected_config = config_name

        # Save the new configuration
        write_json(self.sorting_configuration_folder / f'{config_name}.json', self.configuration_data)

        # Update the GUI
        self.load_config_files()

    @wrap_handler
    def handle_copy_configuration(self, config_name: str, to_copy_config_name: str):
        # Load new configuration form to_copy
        self.selected_config = config_name
        self.configuration_data = load_json(self.sorting_configuration_folder / f'{to_copy_config_name}.json')

        # Save the new configuration
        write_json(self.sorting_configuration_folder / f'{config_name}.json', self.configuration_data)

        # Update the GUI
        self.load_config_files()

    @wrap_handler
    def handle_delete_configuration(self, config_name: str):
        # Create a backup of the configuration
        configuration_data = load_json(self.sorting_configuration_folder / f'{config_name}.json')
        backup_config_name = f'{config_name}_backup_{get_timestamp()}.json'
        write_json(self.configuration_backups_folder / backup_config_name, configuration_data)

        # Delete the configuration
        os.remove(self.sorting_configuration_folder / f'{config_name}.json')

        # Load the first configuration
        self.load_config_files()
        self.selected_config = self.config_files[0].stem
        self.configuration_data = load_json(self.config_files[0])

    @wrap_handler
    def handle_save_configuration(self, config_name: str, sorting_headers: list[str], sorting_data: list[list[str]]):
        update_code = self.active_config == config_name
        configuration_data = {'sorting_headers': sorting_headers, 'sorting_data': sorting_data}

        # Create a backup of the old configuration
        try:
            configuration_data_old = load_json(self.sorting_configuration_folder / f'{config_name}.json')
            backup_config_name = f'{config_name}_backup_{get_timestamp()}.json'
            write_json(self.configuration_backups_folder / backup_config_name, configuration_data_old)
        except Exception:
            logger.exception('Error while trying to backup old config')

        # Save settings to the config file
        write_json(self.sorting_configuration_folder / f'{config_name}.json', configuration_data)

        # Update the GUI
        self.selected_config = config_name
        self.configuration_data = configuration_data

        # Update code if active configuration was changed
        if update_code:
            self.update_code()


def fill_empty_with_left(row):
    for i in range(1, len(row)):
        if row[i] == '':
            row[i] = row[i - 1]
    return row
