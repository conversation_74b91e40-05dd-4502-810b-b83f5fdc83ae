<script lang="ts">
  // Imports
  import { <PERSON><PERSON>, Card, FRow, Input } from "@heliovision/sveltestrap";
  import { gui } from "../stores";
  import { createEventDispatcher } from "svelte";
  import { normalizeUserName } from "../util";

  const {state} = gui;

  let inputFolder = "";
  export let userName: string = "";

  $: if (userName && !inputFolder) {
    const cleaned = normalizeUserName(userName)
    if (cleaned) {
      const previousFolder = $state.users[cleaned]?.folder;
      if (previousFolder && $state.folders.includes(previousFolder)) {
        inputFolder = previousFolder;
      }
    }
  }

  const dispatch = createEventDispatcher();

  function click() {
    const cleaned = normalizeUserName(userName);
    if (!cleaned) return;

    dispatch("start");
    gui.command("start_labelling", {user_name: cleaned, input_folder: inputFolder});
  }
</script>


<Card class="mt-4" color="primary-subtle" title="Selecteer de folder met de foto's die u wenst te labelen">
  <Input
    bind:value={userName}
    labelBefore="Naam"
    placeholder="Enter some text"
    style="margin-top: 10px;"
    color="primary {normalizeUserName(userName) ? 'is-valid' : 'is-invalid'}"
  />

  <Input
    bind:value={inputFolder}
    labelBefore="Afbeeldingen folder"
    style="margin-top: 10px;"
    type="select"
  >
    {#each $state.folders as folder (folder)}
      <option value={folder}>{folder}</option>
    {/each}
  </Input>

  <FRow right>
    <Button
      color="success"
      disabled={!normalizeUserName(userName) || inputFolder === ""}
      on:click={click}
      size="lg"
      style="margin-top: 20px;"
    >
      Start met labellen
    </Button>
  </FRow>
</Card>
