<script lang="ts">
  // Imports
  import { <PERSON><PERSON>, <PERSON>, FRow, Modal } from "@heliovision/sveltestrap";
  import { ButtonGroup, InputGroup, InputGroupText, Spinner } from "@sveltestrap/sveltestrap";
  import { gui } from "../stores"
  import { defaultUserState, POSSIBLE_BUTTONS, } from "../config";
  import { createEventDispatcher } from "svelte";
  import { normalizeUserName } from "../util";

  const {state, liveImages} = gui;
  const dispatch = createEventDispatcher();

  let rawUserName: string;
  // noinspection JSUnusedGlobalSymbols
  export { rawUserName as userName };
  let user_name = normalizeUserName(rawUserName) ?? `INVALID_USERNAME:${rawUserName}`;
  $: userState = $state.users[user_name] ?? defaultUserState;
  let targetSaveTime = 0;  // make save give some feedback, ya know?

  function saveLabelling() {
    targetSaveTime = Date.now();
    gui.command("save_labelling", {user_name, timestamp: targetSaveTime});
  }

  // Extract size and flight from crateImageName
  function extractSize(imageName: string): string {
    const match = imageName.match(/_size([A-Za-z0-9]{2,3})_/);
    return match ? match[1] : 'Unknown';
  };
  $: currentSize = extractSize(userState.current.crateImageName);

  function extractFlight(imageName: string): string {
    const match = imageName.match(/_flight(\d)\./);
    return match ? match[1] : 'Unknown';
  }
  $: currentFlight = extractFlight(userState.current.crateImageName);

  // modal open vars
  let modalStopOpen = false;
  let modalIndexOpen = false;
  let jumpIndex: null | number | string = null;

  $: if (modalIndexOpen && jumpIndex === null) jumpIndex = userState.current.index
</script>

<style>
    .spinner-placeholder {
        width: 350px;
        height: 350px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

<!-- Modals -->
<!-- We can't use ad hoc or prepared modals, because they'd be shown to all users -->

<Modal
  actions={[
      { label: "Stop", action: "stop", color: "danger" },
      { label: "Verdergaan", action: "close", color: "info" },
  ]}
  body="Weet je zeker dat je wilt stoppen?"
  isOpen={modalStopOpen}
  on:action={(e) => {
    if (e.detail === "stop") {
      gui.command("stop_labelling", {user_name});
      dispatch("stop");
    }
    modalStopOpen = false;
  }}
  on:close={() => modalStopOpen = false}
  title="Stoppen"
/>

<Modal
  isOpen={modalIndexOpen}
  on:close={() => modalIndexOpen = false}
  title="Spring naar foto"
>
  <InputGroup>
    <InputGroupText>Foto index</InputGroupText>
    <input
      class="form-control remove-default-arrows"
      type="number"
      bind:value={jumpIndex}
      min="1"
      max={userState.totalAmountOfImages}
    />
    <Button
      color="success"
      disabled={jumpIndex === null || +jumpIndex < 1 || +jumpIndex > userState.totalAmountOfImages}
      on:click={() => {
        if (jumpIndex === null) return;
        gui.command("switch_image", {action: "index", user_name, index: +jumpIndex});
        modalIndexOpen = false;
        jumpIndex = null;
    }}>Spring
    </Button>
  </InputGroup>
  <FRow class="mt-3">
    <Button color="warning" on:click={() => {
      gui.command("switch_image", {action: "first-unlabelled", user_name});
      modalIndexOpen = false;
  }}>Eerste ongelabelde
    </Button>
  </FRow>
</Modal>


<Card color="primary-subtle" title="Gebruiker: {rawUserName}" headerClass="d-flex flex-row justify-content-center">
  <FRow class="justify-content-around" vCenter>
    <Button
      class="fs-2 fw-bold"
      color="danger"
      on:click={() => modalStopOpen = true}
      size="lg"
      style="min-width: 10%;"
    >
      Stop
    </Button>
    <Button
      class="fs-3"
      color="outline-dark"
      on:click={() => modalIndexOpen = true}
      size="lg"
    >
      Krat: {userState.current.index}/{userState.totalAmountOfImages}
    </Button>
    <Button
      class="fs-3"
      color="outline-dark"
      size="lg"
    >
      Grootte: {currentSize}
    </Button>
    <Button
      class="fs-3"
      color="outline-dark"
      size="lg"
    >
      Vlucht: {currentFlight}
    </Button>
    <Button
      class="fs-2 fw-bold"
      color="success"
      loading={userState.lastSavedTimestamp < targetSaveTime}
      on:click={saveLabelling}
      size="lg"
      style="min-width: 10%;"
    >
      Save
    </Button>

  </FRow>
  
  <FRow class="justify-content-around mt-4" vCenter>
    <ButtonGroup vertical>
      {#each [
        ["bi-chevron-left", "previous"],
        ["bi-chevron-bar-left", "first"],
      ] as [icon, action]}
        <Button
          color="dark"
          size="lg"
          class="fs-1"
          disabled="{userState.current.index <= 1}"
          on:click={() => gui.command("switch_image", {action, user_name})}
        >
          <i class="bi {icon}"/>
        </Button>
      {/each}
    </ButtonGroup>
    {#if $liveImages[userState.current.imageId]}
      <figure class="figure">
        <img
          src={$liveImages[userState.current.imageId]}
          caption="Champignons"
          class="rounded mx-auto d-block aspect-ratio-1x1"
          style="width: 1100px;"
          alt="Champignons"
        />
        <figcaption class="figure-caption" style="margin-top: 5px;">{userState.current.crateImageName}</figcaption>
      </figure>
    {:else}
      <div class="spinner-placeholder">
        <Spinner/>
      </div>
    {/if}
    <ButtonGroup vertical>
      {#each [
        ["bi-chevron-right", "next"],
        ["bi-chevron-bar-right", "last"],
      ] as [icon, action]}
        <Button
          color="dark"
          size="lg"
          class="fs-1"
          disabled="{userState.current.index >= userState.totalAmountOfImages}"
          on:click={() => gui.command("switch_image", {action, user_name})}
        >
          <i class="bi {icon}"/>
        </Button>
      {/each}
    </ButtonGroup>
  </FRow>
  <FRow class="justify-content-around mt-4" vCenter>
    {#each POSSIBLE_BUTTONS as button}
      <Button
        color={userState.current.label === button ? "success": "dark"}
        on:click={() => gui.command("button", {button, user_name})}
        size="lg"
        style="min-width: 5%;"
      >
        <div class="fs-3 fw-bold">{button}</div>
      </Button>
    {/each}
    <Button
      color="warning"
      on:click={() => gui.command("button", {button:null, user_name})}
      size="lg"
      style="min-width: 5%;"
    >
      <div class="fs-3 fw-bold">Clear</div>
    </Button>

  </FRow>
</Card>

