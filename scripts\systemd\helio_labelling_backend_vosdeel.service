[Unit]
Description=Heliovision labelling tool backend at the vosdeel site
Requires=network-online.target helio_labelling_gui_vosdeel.service
After=network-online.target helio_labelling_gui_vosdeel.service

[Service]
WorkingDirectory=/home/<USER>/Documents/project
User=heliovision
ExecStart=/home/<USER>/.local/share/virtualenvs/code-HgKXYTTI/bin/python3 /home/<USER>/Documents/project/code/labelling/main.py
Type=simple
KillMode=control-group
Restart=always
RemainAfterExit=no
RestartSec=30s

[Install]
WantedBy=multi-user.target
