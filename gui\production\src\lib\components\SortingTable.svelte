<script lang="ts">
  import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@sveltestrap/sveltestrap";
  import { Button } from "@heliovision/sveltestrap";
  import { CrossSign, LayoutCol, LayoutRow, LineSign, PlusSign } from ".";

  export let tableHeaders: string[] = []; // Example: ['timestamp', 'attribute1', 'attribute2']
  export let tableData: string[][] = []; // Example: [[timevalue_1, val1_1, val2_1], [timevalue_2, val1_2, val2_2]]
  $: firstColumnElements = ["", ...new Set(tableData.map(row => row[0]).filter(item => item !== ""))];

  let focusedRowIndex: number | null = null;
  let focusedColIndex: number | null = null;
  let focusedHeaderIndex: number | null = null;

  let isDeleteModalOpen = false;
  let deleteType: 'Kolom' | 'Rij' | null = null;
  let deleteIndex: number | null = null;

  function getRowBackgroundColor(index: number) {
    return index % 2 === 0 ? 'white' : '#f2f2f2';
  }

  function handleFocusInHeader(headerIndex: number) {
    setTimeout(() => {
      focusedHeaderIndex = headerIndex;
    }, 200); // Give handleFocusOut time to be handled first
  }

  function handleFocusInCell(rowIndex: number, colIndex: number) {
    setTimeout(() => {
      focusedRowIndex = rowIndex;
      focusedColIndex = colIndex;
    }, 200); // Give handleFocusOut time to be handled first
  }

  function handleFocusOut() {
    setTimeout(() => {
      focusedRowIndex = null;
      focusedColIndex = null;
      focusedHeaderIndex = null;
    }, 150); // Give the buttons time to react before they are removed
  }

  function addRow(index: number) {
    const newRow = tableHeaders.map(() => '');
    tableData = [
      ...tableData.slice(0, index),
      newRow,
      ...tableData.slice(index)
    ];
  }

  function addColumn(index: number) {
    tableHeaders = [
      ...tableHeaders.slice(0, index),
      '',
      ...tableHeaders.slice(index)
    ];
    tableData = tableData.map(row => [
      ...row.slice(0, index),
      '',
      ...row.slice(index)
    ]);
  }

  function toggleDeleteModal(type: 'Kolom' | 'Rij' | null = null, index: number | null = null) {
    isDeleteModalOpen = !isDeleteModalOpen;
    deleteType = type;
    deleteIndex = index;
  }

  function deleteRow(index: number | null) {
    tableData = tableData.filter((_, i) => i !== index);
    isDeleteModalOpen = false;
  }

  function deleteColumn(index: number | null) {
    tableHeaders = tableHeaders.filter((_, i) => i !== index);
    tableData = tableData.map(row => row.filter((_, i) => i !== index));
    isDeleteModalOpen = false;
  }
</script>

<style>
    .table_container {
        display: block;
        height: 100%; /* Adjust as necessary via style */
        width: 100%; /* Adjust as necessary via style */
        overflow: auto; /* Allows both horizontal and vertical scrolling */
        border-style: solid;
        border-color: #70B845;
        border-radius: 4px;
    }

    table {
        width: 100%;
        height: 100%;
        border-collapse: separate;
        min-width: max-content; /* Ensures table doesn't shrink below content size */
    }

    table, th, td {
        border: 1px solid black;
        border-spacing: 0px;
        box-sizing: border-box !important;
        table-layout: fixed !important;
    }

    th, td {
        text-align: center;
        justify-content: center;
        padding: 0.3em;
        overflow: hidden; /* Prevent content from overflowing cells */
        box-sizing: border-box;
        text-overflow: ellipsis; /* Add an ellipsis to overflowing content */
        white-space: nowrap; /* Keep content on a single line */
        max-width: 11em; /* This actually becomes the min-width! Not sure why though. Does not work when using percents.*/
    }

    .sticky-header {
        position: sticky;
        top: 0px;
        background: white; /* Ensures the header background isn't transparent */
        z-index: 10; /* Keep the header at the top when scrolling */
    }

    .sticky-col {
        position: sticky;
        left: 0px;
        z-index: 5; /* Stack order for sticky columns */
    }

    .sticky-intersect {
        z-index: 15; /* Highest stack order */
    }

    input[type="text"] {
        width: 95%;
        border: none;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        background: transparent;
        text-align: center;
        text-overflow: ellipsis;
    }

    input[type="text"]:focus {
        outline-color: black;
        outline-width: 5px;
    }

    select {
        width: 100%;
        border: 0px;
        border-radius: 10px;
        appearance: none;
    }

    option {
        padding: 0px;
        margin: 0px;
        text-align: center;
    }

    .delete-button {
        background: transparent;
        border: none;
        cursor: pointer;
        font-size: 1em;
        color: red;
        margin: 0px;
        padding: 0px;
    }

    .add-button {
        height: 50%;
        background: transparent;
        border: none;
        cursor: pointer;
        font-size: 1em;
        color: green;
        margin: 0px;
        padding: 0px;
    }
</style>

<div {...$$restProps} class="table_container {$$restProps.class}">
  <table>
    <thead>
    <tr>
      {#each tableHeaders as header, headerIndex}
        {#if headerIndex === 0}
          <th class="sticky-header sticky-col sticky-intersect">
            {header}
          </th>
        {:else}
          <th class="sticky-header">
            <LayoutRow>
              {#if focusedHeaderIndex === headerIndex}
                <LayoutCol style="flex-direction: column; width: 10%; flex: 1 0 10%;">
                  <button class="add-button" on:click={() => addColumn(headerIndex)}>
                    <PlusSign width={12} height={12}/>
                  </button>
                </LayoutCol>
              {/if}
              <LayoutCol style="width: 70%; flex: 1 0 70%;">
                <input
                  type="text"
                  bind:value={tableHeaders[headerIndex]}
                  on:focusin={() => handleFocusInHeader(headerIndex)}
                  on:focusout={handleFocusOut}
                />
              </LayoutCol>
              {#if focusedHeaderIndex === headerIndex}
                <LayoutCol style="width: 10%; flex: 1 0 10%;">
                  <button class="delete-button" on:click={() => toggleDeleteModal('Kolom', headerIndex)}>
                    <CrossSign width={12} height={12}/>
                  </button>
                </LayoutCol>
                <LayoutCol style="flex-direction: column; width: 10%; flex: 1 0 10%;">
                  <button class="add-button" on:click={() => addColumn(headerIndex + 1)}>
                    <PlusSign width={12} height={12}/>
                  </button>
                </LayoutCol>
              {/if}
            </LayoutRow>
          </th>
        {/if}
      {/each}
    </tr>
    </thead>
    <tbody>
    {#each tableData as row, rowIndex}
      <tr style="background-color: {getRowBackgroundColor(rowIndex)};">
        {#each row as cell, colIndex}
          {#if colIndex === 0}
            <td class="sticky-col" style="background-color: {getRowBackgroundColor(rowIndex)};">
              <LayoutRow>
                {#if focusedRowIndex === rowIndex && focusedColIndex === colIndex}
                  <LayoutCol style="width: 10%; flex: 1 0 10%;">
                    <button class="delete-button" on:click={() => toggleDeleteModal('Rij', rowIndex)}>
                      <CrossSign width={12} height={12}/>
                    </button>
                  </LayoutCol>
                  <LayoutCol style="flex-direction: column; width: 10%; flex: 1 0 10%;">
                    <button class="add-button" on:click={() => addRow(rowIndex)}>
                      <PlusSign width={12} height={12}/>
                    </button>
                    <LineSign width={12} height={3} color={'green'}/>
                    <button class="add-button" on:click={() => addRow(rowIndex + 1)}>
                      <PlusSign width={12} height={12}/>
                    </button>
                  </LayoutCol>
                {/if}
                <LayoutCol style="width: 80%; flex: 1 0 80%;">
                  <input
                    type="text"
                    bind:value={tableData[rowIndex][colIndex]}
                    on:focusin={() => handleFocusInCell(rowIndex, colIndex)}
                    on:focusout={handleFocusOut}
                  />
                </LayoutCol>
              </LayoutRow>
            </td>
          {:else}
            <td>
              <select bind:value={tableData[rowIndex][colIndex]}
                      style="background-color: {getRowBackgroundColor(rowIndex)};">
                {#if firstColumnElements}
                  {#each firstColumnElements as element}
                    <option value={element}>{element}</option>
                  {/each}
                {/if}
              </select>
            </td>
          {/if}
        {/each}
      </tr>
    {/each}
    </tbody>
  </table>
</div>

<!-- Modal for deleting rows or columns -->
<Modal isOpen={isDeleteModalOpen} toggle={toggleDeleteModal}>
  <ModalHeader toggle={toggleDeleteModal}>
    {deleteType} verwijderen?
  </ModalHeader>
  <ModalFooter>
    <Button color="warning"
            on:click={() => deleteType === 'Kolom' ? deleteColumn(deleteIndex) : deleteRow(deleteIndex)}>
      Verwijderen
    </Button>
    <Button color="secondary" on:click={toggleDeleteModal}>
      Cancel
    </Button>
  </ModalFooter>
</Modal>
