import asyncio

from heliovision.gui import Gui

from configuration_manager import ConfigurationManager


async def main():
    gui = Gui({})
    gui.state['connecting'] = True
    gui.state['vision_status'] = True
    gui.state['lights_status'] = True
    gui.state['current'] = None
    gui.state['current_crate_timestamp'] = None
    gui.state['config'] = None

    config_manager = ConfigurationManager(gui)
    gui.register_multi_command_handler(config_manager)

    gui.state['config'] = config_manager

    await gui.serve()

    gui.state['connecting'] = False
    gui.broadcast_state()

    await asyncio.Future()  # Keep the event loop running


if __name__ == '__main__':
    asyncio.run(main())
