<script lang="ts">
    // Imports
    import { gui } from "../../stores.js";
    import { <PERSON><PERSON>, <PERSON><PERSON>Header, <PERSON>dalFooter } from "@sveltestrap/sveltestrap";
    import { But<PERSON> } from "@heliovision/sveltestrap";

    export let selectedConfig: string | null = null;

    let isModalOpen = false;
    let newConfigName = '';

    function toggleModal() {
        isModalOpen = !isModalOpen;
        newConfigName = ''; // Reset input field when modal is closed
    }

    function deleteConfiguration() {
        gui.command("delete_configuration", [selectedConfig,]);
        toggleModal();
    }
</script>

<!-- Button to open the modal -->
<Button 
    color="warning"
    on:click={toggleModal}
    style="font-size: 1.4em; padding-top: 4px;"
    >
    Verwijderen
</Button>

<!-- Modal for entering the new configuration name -->
<Modal isOpen={isModalOpen} toggle={toggleModal}>
    <ModalHeader toggle={toggleModal}>
        Huidige configuratie verwijderen?
    </ModalHeader>
    <ModalFooter>
        <Button color="warning" on:click={deleteConfiguration}>
            Verwijderen
        </Button>
        <Button color="secondary" on:click={toggleModal}>
            Cancel
        </Button>
    </ModalFooter>
</Modal>

