# System settings which are the same for all sites
[schedule]
# Define active times (hour, minute) for each weekday (24-hour format) (monday is = 0)
active_times = {0 = [[6, 10], [17, 0]], 1 = [[6, 10], [17, 0]], 2 = [[6, 10], [13, 0]], 3 = [[6, 10], [13, 0]]}

check_interval_time_seconds = 60

[system]
sanity_message_interval_seconds = 900
reset_wait_time_seconds = 20

[logger]
rotation = "00:00"
retention = "6 months"
