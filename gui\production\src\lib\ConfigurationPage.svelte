<script lang="ts">
  // Imports
  import { gui } from "../stores.js";
  import { Card } from "@heliovision/sveltestrap";
  import {
    ActivateConfigurationButton,
    ConfigurationSelection,
    DeleteConfigurationButton,
    LayoutCol,
    LayoutRow,
    NewConfigurationButton,
    SaveConfigurationButton,
    SortingTable
  } from "./components";

  const state = gui.state

  $: activeConfig = $state.config?.active_config;
  $: selectedConfig = $state.config?.selected_config;
  $: configFiles = $state.config?.config_files || [];
  $: sortingHeaders = $state.config?.configuration_data?.sorting_headers || [];
  $: sortingData = $state.config?.configuration_data?.sorting_data || [];

  let sortingHeadersLast: string[] = [];
  let sortingDataLast: string[][] = [];
  let sortingHeadersShown: string[] = [];
  let sortingDataShown: string[][] = [];

  function areArraysEqual(array1: unknown[], array2: unknown[]) {
    return JSON.stringify(array1) === JSON.stringify(array2);
  }

  function updateShown() {
    if (areArraysEqual(JSON.parse(JSON.stringify(sortingHeaders)), sortingHeadersLast) && areArraysEqual(JSON.parse(JSON.stringify(sortingData)), sortingDataLast)) {
      return
    }
    sortingHeadersLast = JSON.parse(JSON.stringify(sortingHeaders));
    sortingDataLast = JSON.parse(JSON.stringify(sortingData));
    sortingHeadersShown = JSON.parse(JSON.stringify(sortingHeaders));
    sortingDataShown = JSON.parse(JSON.stringify(sortingData));
  }

  $: sortingHeaders && updateShown();
  $: sortingData && updateShown();
</script>

<Card class="mt-4" color="primary-subtle" title="Sorteringen">
  <Card class="mt-4" color="info-subtle" style="margin-top: 0px !important" title="">
    <LayoutRow>
      <LayoutCol style="flex: 1 0 35%;">
        <LayoutRow>
          <LayoutCol style="flex: 1 0 25%; justify-content: flex-end; padding-right: 0.5em;">
            <h4>Selecteer:</h4>
          </LayoutCol>
          <LayoutCol style="flex: 1 0 55%;">
            <ConfigurationSelection bind:configFiles={configFiles} bind:selectedConfig={selectedConfig}/>
          </LayoutCol>
          <LayoutCol style="flex: 1 0 15%; justify-content: flex-start; padding-left: 0.5em;">
            <NewConfigurationButton bind:configFiles={configFiles} bind:selectedConfig={selectedConfig}/>
          </LayoutCol>
        </LayoutRow>
      </LayoutCol>
      <LayoutCol style="flex: 1 0 30%;">
        {#if (activeConfig === selectedConfig && areArraysEqual(sortingHeadersShown, sortingHeaders) && areArraysEqual(sortingDataShown, sortingData))}
          <h4>Actief</h4>
        {:else}
          <h4>Niet actief</h4>
        {/if}
      </LayoutCol>
      <LayoutCol style="flex: 1 0 12%; justify-content: flex-end; padding-right: 0.5em;">
        <ActivateConfigurationButton
          bind:selectedConfig={selectedConfig}
          bind:sortingData={sortingData}
          bind:sortingDataShown={sortingDataShown}
          bind:sortingHeadersShown={sortingHeadersShown}
          sortingHeaders={sortingHeaders}
        />
      </LayoutCol>
      <LayoutCol style="flex: 1 0 8%; justify-content: flex-end; padding-right: 0.5em;">
        <SaveConfigurationButton selectedConfig={selectedConfig} sortingData={sortingDataShown}
                                 sortingHeaders={sortingHeadersShown}/>
      </LayoutCol>
      <LayoutCol style="flex: 1 0 10%; justify-content: flex-start;">
        <DeleteConfigurationButton selectedConfig={selectedConfig}/>
      </LayoutCol>
    </LayoutRow>
  </Card>
  <Card class="mt-4" color="success-subtle" title="">
    <div style='margin: 20px;'>
      <SortingTable bind:tableData={sortingDataShown} bind:tableHeaders={sortingHeadersShown}
                    style="height: 35em; width: 100%;"/>
    </div>
  </Card>
</Card>
