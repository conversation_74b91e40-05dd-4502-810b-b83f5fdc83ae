from datetime import datetime

from loguru import logger
from reactivex import Subject

from protocols import NewClassification, NewClassificationFeedback, NewCrateInfo
from telegram import Telegram


class TelegramHandler:
    def __init__(self) -> None:
        """
        Initialize the telegram handler with a callback for sending responses to the PLC.
        """
        self.incoming_telegram_subject = Subject[Telegram]()
        self.outgoing_telegram_subject = Subject[Telegram]()

        self.new_watchdog_message_subject = Subject[Telegram]()
        self.new_crate_subject = Subject[NewCrateInfo]()
        self.new_classification_feedback_subject = Subject[NewClassificationFeedback]()

        self.incoming_classification_for_crate_subject = Subject[NewClassification]()

        self.incoming_classification_for_crate_subject.subscribe(self.send_crate_classification_to_plc)
        self.incoming_telegram_subject.subscribe(self.handle_telegram)

    def handle_telegram(self, telegram: Telegram) -> None:
        """
        Handle a received telegram from the PLC, based on the command type of the telegram.

        Args:
            telegram: The received telegram to handle. Must be a Telegram instance.
        """

        if telegram.repeat_flag:
            logger.warning(
                f'Received a repeated telegram from sender {telegram.sender_id!r} with Command: {telegram.cmd!r} and Telegram Nr: {telegram.telegram_nr!r}. Ignoring this telegram.'
            )
            return

        logger.info(
            f'Processing telegram from sender: {telegram.sender_id!r}, Command: {telegram.cmd!r}, Telegram Nr: {telegram.telegram_nr!r}.'
        )

        match telegram.cmd:
            case 1:  # Incoming watchdog message
                self.new_watchdog_message_subject.on_next(telegram)
            case 55:  # New crate is on its way
                self._handle_new_crate(telegram)
            case 57:  # New classification for a previously seen crate
                self._handle_classification_feedback(telegram)
            case _:
                logger.warning(f'Unknown command {telegram.cmd!r}. Ignoring this telegram.')

    def _handle_new_crate(self, telegram: Telegram) -> None:
        """
        Handle a new crate telegram from the PLC.
        An event is emitted per new crate, which can be subscribed to by other components.

        Args:
            telegram: The telegram to handle. Must be a Telegram instance.
        """
        logger.info(
            f'Received a new crate telegram. Emitting new crate event for crate with trace_id: {telegram.trace_id!r}.'
        )
        self.new_crate_subject.on_next(
            NewCrateInfo(
                telegram_nr=telegram.telegram_nr,
                trace_id=telegram.trace_id,
                timestamp=datetime.now(),
                fill_station=telegram.new_crate_fill_station,
                cell_id=telegram.new_crate_cell_id,
                flight_id=telegram.new_crate_flight_id,
                possible_classes=telegram.new_crate_possible_classes,
            )
        )

    def _handle_classification_feedback(self, telegram: Telegram) -> None:
        """
        Handle a classification feedback telegram from the PLC.
        An event is emitted per classification feedback (if feedback is valid), which can be subscribed to by other components.

        Args:
            telegram: The telegram to handle. Must be a Telegram instance.
        """
        feedback_data = telegram.classification_feedback_button_pressed
        logger.info(
            f'Received classification feedback telegram from sender {telegram.sender_id!r} with trace_id: {telegram.trace_id!r} and classification data: {feedback_data!r}.'
        )

        feedback = NewClassificationFeedback(
            trace_id=telegram.trace_id,
            button_pressed=False,
            classification=None,
            manual_classification=None,
        )

        if not feedback_data or feedback_data in {'0', '1'}:
            self.new_classification_feedback_subject.on_next(feedback)
            return

        feedback.button_pressed = True
        feedback_data_parts = feedback_data.split('_')
        feedback.classification = feedback_data_parts[0]

        if len(feedback_data_parts) > 1:
            feedback.manual_classification = feedback_data_parts[1] == '1'
            if not feedback.manual_classification:
                logger.warning(f'Invalid classification feedback data: {feedback_data!r}. Skipping this feedback.')
                return
        else:
            feedback.manual_classification = False

        self.new_classification_feedback_subject.on_next(feedback)
        return

    def send_crate_classification_to_plc(self, data: NewClassification) -> None:
        """
        Send a classification for a crate to the PLC.

        Args:
            data: The classification data to send. Must be a NewClassification instance.
        """
        logger.info(
            f'Sending classification ({data.classification!r}) for crate with trace_id: {data.trace_id!r} to the PLC.'
        )
        response = Telegram()
        response.repeat_flag = False
        response.sender_id = 'HVIS'
        response.cmd = 56
        response.telegram_nr = data.telegram_nr
        response.trace_id = data.trace_id
        response.ai_inference_classification = data.classification

        logger.debug(f'Created telegram: {str(response)!r}, to be sent to the PLC.')
        self.outgoing_telegram_subject.on_next(response)
