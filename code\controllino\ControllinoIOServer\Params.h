#ifndef Params_h
#define Params_h

// Controllino Maxi AUTOMATION Digital inputs (DI0, DI1, DI2, DI3)
const int NB_DIGITAL_INPUT_PINS = 4;
int digitalInputPins[NB_DIGITAL_INPUT_PINS] = { 66, 67, 10, 11};  

// Controllino Maxi AUTOMATION ANALOG inputs (all used as digital inputs with 'digitalRead')
//const int NB_DIGITAL_INPUT_PINS = 14;
//int digitalInputPins[NB_DIGITAL_INPUT_PINS] = { 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69 };  

// Controllino Maxi ANALOG inputs (all used as digital inputs with 'digitalRead')
//const int NB_DIGITAL_INPUT_PINS = 10;
//int digitalInputPins[NB_DIGITAL_INPUT_PINS] = { 54, 55, 56, 57, 58, 59, 60, 61, 62, 63 };  

#endif
