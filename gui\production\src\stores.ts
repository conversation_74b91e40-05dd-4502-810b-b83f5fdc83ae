import { websocketConfig } from "./config";
import { GUI } from "@heliovision/gui";

const {host, port} = websocketConfig;

type Scan = {
  flight: string
  quality_preference: string
  quality_vision: string
  quality_label_vision: string
}

type Config = {
  active_config: string | null
  selected_config: string | null
  config_files: string[] | null
  configuration_data: {
    sorting_headers: string[]
    sorting_data: string[][]
  } | null
}

type State = {
  connecting: boolean
  vision_status: boolean,
  lights_status: boolean
  current: Scan | null,
  current_crate_timestamp: number | null, // millis
  config: Config | null
}

export const gui = new GUI<State>(
  {
    'connecting': true,
    'vision_status': false,
    'lights_status': false,
    'current': null,
    'current_crate_timestamp': null,
    'config': null
  },
  `ws://${host}:${port}`,
);

