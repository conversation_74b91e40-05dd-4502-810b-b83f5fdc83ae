import csv
import os
from abc import ABC, abstractmethod
from typing import Any

import cv2 as cv
import numpy as np

from protocols import NewCrateInfo


class ImageSaver(ABC):
    @abstractmethod
    def save(self, image_name: str, image: np.ndarray) -> None: ...


class SimpleImageSaver:
    def __init__(self, image_dir: str):
        self.image_dir = image_dir

    def save(self, image_name: str, image: np.ndarray) -> None:
        image_path = os.path.join(self.image_dir, image_name)
        cv.imwrite(image_path, image)


class CrateImageSaver(SimpleImageSaver):
    def __init__(self, crate: NewCrateInfo, **kwargs):
        super().__init__(**kwargs)
        self.crate = crate
        self.file_name = os.path.join(self.image_dir, 'crate_info.csv')

    def save(self, image_name: str, image: Any) -> None:
        super().save(image_name, image)
        self.log(image_name=image_name)

    def log(self, image_name: str) -> None:
        with open(self.file_name, mode='a', newline='', encoding='utf-8') as file:
            # Add the 'image_path' to the list of fields
            fieldnames = list(NewCrateInfo.__annotations__.keys()) + ['image_name']
            writer = csv.DictWriter(file, fieldnames=fieldnames)

            # Write the header only if the file is empty
            if file.tell() == 0:
                writer.writeheader()

            # Convert the dataclass instance to a dictionary and add the image path
            crate_dict = self.crate.__dict__.copy()
            crate_dict['timestamp'] = self.crate.timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[-3]
            crate_dict['image_path'] = image_name
            # Write the row
            writer.writerow(crate_dict)
