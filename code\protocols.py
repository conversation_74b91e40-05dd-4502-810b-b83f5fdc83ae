from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Protocol

import numpy as np


@dataclass
class NewCrateInfo:
    telegram_nr: str
    trace_id: str
    timestamp: datetime
    fill_station: str
    cell_id: str
    flight_id: str
    possible_classes: str

    def __post_init__(self):
        if len(self.trace_id) != 12:
            raise ValueError(
                f'Invalid trace ID. Must be exactlty 12 characters long (zero-padded), was {self.trace_id!r}.'
            )

        telegram_nr_int = int(self.telegram_nr)
        if not 1 <= telegram_nr_int <= 999_999:
            raise ValueError(f'Invalid telegram number: {telegram_nr_int!r}. Must be between 1 and 999_999.')

        self.telegram_nr = str(telegram_nr_int).zfill(6)

        if len(self.possible_classes) != 8:
            raise ValueError(
                f'Invalid possible classes. Must be exactly 8 characters long, was {self.possible_classes!r}.'
            )


@dataclass
class NewClassification:
    telegram_nr: str
    trace_id: str
    classification: str

    def __post_init__(self):
        if len(self.classification) > 2 or self.classification not in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']:
            raise ValueError(
                "Invalid classification. Must be one of 'A', 'B', 'C', 'D', 'E', 'F', 'G' or 'H'."
                f'Was {self.classification!r}.'
            )

        if len(self.trace_id) != 12:
            raise ValueError(
                f'Invalid trace ID. Must be exactlty 12 characters long (zero-padded), was {self.trace_id!r}.'
            )

        telegram_nr_int = int(self.telegram_nr)
        if not 1 <= telegram_nr_int <= 999_999:
            raise ValueError(f'Invalid telegram number: {telegram_nr_int!r}. Must be between 1 and 999_999.')

        self.telegram_nr = str(telegram_nr_int).zfill(6)


@dataclass
class NewClassificationFeedback:
    trace_id: str
    button_pressed: bool
    classification: Optional[str] = None
    manual_classification: Optional[bool] = None

    def __post_init__(self):
        if len(self.trace_id) != 12:
            raise ValueError(
                f'Invalid trace ID. Must be exactlty 12 characters long (zero-padded), was {self.trace_id!r}.'
            )

        if not isinstance(self.button_pressed, bool):
            raise ValueError(f'Invalid button_pressed value. Must be a boolean, was {self.button_pressed!r}.')

        if self.classification is not None:
            if len(self.classification) > 2 or self.classification not in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']:
                raise ValueError(
                    "Invalid classification. Must be one of 'A', 'B', 'C', 'D', 'E', 'F', 'G' or 'H'."
                    f'Was {self.classification!r}.'
                )

        if self.manual_classification is not None and not isinstance(self.manual_classification, bool):
            raise ValueError(
                f'Invalid manual_classification value. Must be None or a boolean, was {self.manual_classification!r}.'
            )


class ClassifierProtocol(Protocol):
    async def classify(self, image: np.ndarray, info: NewCrateInfo) -> str: ...
