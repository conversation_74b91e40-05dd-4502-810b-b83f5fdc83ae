<script lang="ts">
  // Imports
  import { gui } from "../../stores.js";
  import { Input, <PERSON>dal, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from "@sveltestrap/sveltestrap";
  import { Button } from "@heliovision/sveltestrap";
  import { PlusSign } from ".";

  export let selectedConfig: string | null = null;
  export let configFiles: string[] = [];

  let isModalOpen = false;
  let newConfigName = '';

  function toggleModal() {
    isModalOpen = !isModalOpen;
    newConfigName = ''; // Reset input field when modal is closed
  }

  function createNewConfiguration() {
    if (!checkIfNameIsValid(newConfigName)) {
      return;
    }
    gui.command("create_configuration", [newConfigName,]);
    configFiles = [...configFiles, newConfigName];
    selectedConfig = newConfigName;
    toggleModal();
  }

  function copyConfiguration() {
    if (!checkIfNameIsValid(newConfigName)) {
      return;
    }
    gui.command("copy_configuration", [newConfigName, selectedConfig]);
    configFiles = [...configFiles, newConfigName];
    selectedConfig = newConfigName;
    toggleModal();
  }

  function checkIfNameIsValid(name: string) {
    if (name.trim() === '') {
      alert('Geef een geldige naam op.');
      return false;
    }
    if (configFiles.includes(name)) {
      alert('Naam is reeds in gebruik.');
      return false;
    }
    return true;
  }
</script>

<!-- Button to open the modal -->
<Button
  on:click={toggleModal}
  style="border-radius: 5px; padding: 0.5em; border: 0px; background-color: gray;"
>
  <PlusSign/>
</Button>

<!-- Modal for entering the new configuration name -->
<Modal isOpen={isModalOpen} toggle={toggleModal}>
  <ModalHeader toggle={toggleModal}>Voeg nieuwe configuratie toe</ModalHeader>
  <ModalBody>
    <Input
      bind:value={newConfigName}
      placeholder="Geef de naam voor de nieuwe configuratie"
      type="text"
    />
  </ModalBody>
  <ModalFooter>
    <Button color="primary" on:click={createNewConfiguration}>
      Toevoegen
    </Button>
    <Button color="primary" on:click={copyConfiguration}>
      Kopieëren
    </Button>
    <Button color="secondary" on:click={toggleModal}>
      Cancel
    </Button>
  </ModalFooter>
</Modal>
