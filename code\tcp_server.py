import asyncio

from loguru import logger
from reactivex import Subject

from telegram import Telegram

_tasks = set()


class TcpServer:
    def __init__(
        self,
        host: str,
        port: int,
    ) -> None:
        """
        Initialize the TCP/IP server object.

        Args:
            host: str The host address of the server.
            port: int The port number of the server.
        """
        self._server: asyncio.Server | None = None
        self._clients: set[asyncio.StreamWriter] = set()
        self._write_locks: dict[asyncio.StreamWriter, asyncio.Lock] = {}

        self._host: str = host
        self._port: int = port

        self.incoming_telegram_subject = Subject[Telegram]()
        self.outgoing_telegram_subject = Subject[Telegram]()

        self.outgoing_telegram_subject.subscribe(on_next=self.broadcast_telegram)

    async def start_server(self) -> None:
        """
        Start the TCP/IP server. It is served forever until the 'stop_server' method is called.
        Host and port are defined in the class constructor.
        """
        self._server = await asyncio.start_server(self.handle_client_connection, self._host, self._port)
        host, port = self._server.sockets[0].getsockname()
        logger.info(f'TCP/IP server started on {host!r}:{port!r}.')
        async with self._server:
            await self._server.serve_forever()

    async def stop_server(self) -> None:
        """
        Stop the TCP/IP server and close all the connections with the clients.
        """
        logger.warning('Stopping the TCP/IP server...')
        if self._server:
            self._server.close()
            await self._server.wait_closed()
            logger.warning('Server was stopped.')
        for writer in list(self._clients):
            await self._close_client(writer)

        self._clients.clear()
        self._write_locks.clear()

    async def handle_client_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter) -> None:
        """
        Handle the client connection. This method is called when a new client connects to the server.

        Args:
            reader: asyncio.StreamReader The reader object to read the incoming data.
            writer: asyncio.StreamWriter The writer object to write the outgoing data.

        Raises:
            asyncio.CancelledError: If the connection is cancelled.
            asyncio.IncompleteReadError: If the connection is closed unexpectedly.
            ConnectionError: If the connection fails.
            OSError: If an OS-related error occurs.
        """
        client_address = writer.get_extra_info('peername')
        logger.info(f'New client connected to server with address: {client_address!r}')
        self._clients.add(writer)
        self._write_locks[writer] = asyncio.Lock()

        try:
            while True:
                data = await reader.read(Telegram.NB_BYTES)
                logger.info(f'Received raw telegram from {client_address!r}: {data!r}')
                if not data:
                    logger.info(f'Client {client_address!r} has disconnected.')
                    break
                try:
                    logger.debug('Handling the received telegram now...')
                    self.incoming_telegram_subject.on_next(Telegram.create_from_bytes(data))
                    logger.info('Successfully handled the received telegram.')
                except ValueError as e:
                    logger.error(f'Incoming telegram ({data!r}) is not valid. Skipping this incoming telegram: {e}')
        except asyncio.CancelledError as e:
            logger.error(f'Connection with {client_address!r} cancelled.', exception=e)
        except asyncio.IncompleteReadError as e:
            logger.error(
                f'Connection with {client_address!r} was closed unexpectedly.',
                exception=e,
            )
        except ConnectionError as e:
            logger.error(f'Connection with {client_address!r} failed.', exception=e)
        except OSError as e:
            logger.error(f'OS-related error whilst connected to {client_address!r}.', exception=e)
        finally:
            logger.error(f'Closing connection with {client_address!r} from the server side.')
            await self._close_client(writer)

    async def _close_client(self, writer: asyncio.StreamWriter) -> None:
        """
        Close the connection with the client.

        Args:
            writer: asyncio.StreamWriter The writer object to close.
        """
        client_address = writer.get_extra_info('peername')
        if writer in self._clients:
            try:
                logger.debug(f'Closing connection with {client_address!r}...')
                writer.close()
                await writer.wait_closed()
                logger.warning(f'Connection with {client_address!r} closed.')
            except OSError as e:
                logger.error(
                    f'Failed to properly close the write connection of client: {client_address!r}',
                    exception=e,
                )
            finally:
                self._clients.discard(writer)
                self._write_locks.pop(writer, None)
        else:
            logger.warning(f'Connection with {client_address!r} was already closed.')

    def broadcast_telegram(self, telegram: Telegram):
        """
        Broadcast a telegram to all the connected clients.

        Args:
            telegram: Telegram The telegram to broadcast. Should be a Telegram object.
        """
        for writer in self._clients:
            task = asyncio.create_task(self.send_telegram_async(writer, telegram))
            _tasks.add(task)
            task.add_done_callback(lambda _: _tasks.remove(task))

    async def send_telegram_async(self, writer: asyncio.StreamWriter, telegram: Telegram) -> bool:
        """
        Send a telegram to the client asynchronously.
        Telegrams must be of the same length as defined in the class constructor

        Args:
            writer: asyncio.StreamWriter The writer object to write the telegram to.
            telegram: The telegram to be sent. Should be a Telegram object.

        Returns:
            bool True if the telegram was sent successfully, False otherwise.

        Raises:
            OSError: If an OS-related error occurs.
        """
        client_address = writer.get_extra_info('peername')
        logger.info(f'Trying to send telegram({str(telegram)!r}) to {client_address!r} (PLC)...')

        if writer.is_closing():
            logger.error(f"Writer for {client_address!r} is closing, can't send the telegram.")
            return False

        lock = self._write_locks.get(writer)
        if not lock:
            logger.error(f"No write lock found for {client_address!r}. Can't send telegram.")
            return False

        logger.debug('Acquiring write lock...')
        async with lock:
            try:
                logger.debug('Write lock acquired, trying to send the telegram...')
                writer.write(telegram.to_bytes())
                await writer.drain()
                logger.info(f'Succesfully sent the telegram to {client_address!r} (PLC).')
                return True
            except OSError as e:
                logger.error(f'Failed to send the telegram to {client_address!r}', exception=e)
                return False
