import asyncio
import unittest
from unittest.mock import AsyncMock, MagicMock, patch

from reactivex import Subject

from tcp_server import TcpServer
from telegram import Telegram


class TestTcpServer(unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        self.host: str = '0.0.0.0'
        self.port: int = 5509
        self.server: TcpServer = TcpServer(host=self.host, port=self.port)

    async def test_if_server_starts_and_stops(self):
        with patch('asyncio.start_server', new_callable=AsyncMock) as mock_start_server:
            mock_server_instance = AsyncMock()
            mock_server_instance.close = MagicMock()
            mock_socket = MagicMock()
            mock_socket.getsockname.return_value = (self.host, self.port)
            mock_server_instance.sockets = [mock_socket]
            mock_start_server.return_value = mock_server_instance

            await self.server.start_server()
            mock_start_server.assert_called_once_with(self.server.handle_client_connection, self.host, self.port)
            self.assertEqual(self.server._server, mock_server_instance)

            await self.server.stop_server()
            mock_server_instance.close.assert_called_once()
            mock_server_instance.wait_closed.assert_awaited_once()

    async def test_handle_client_connection(self):
        telegram = Telegram.create_from_bytes(
            b'\x020PLC155000123000123456789__2500030002ABC00FGH_____________________________________________________\x03'
        )
        mock_reader = AsyncMock()
        mock_writer = AsyncMock()
        mock_writer.get_extra_info.return_value = ('127.0.0.1', 1234)
        mock_reader.read.side_effect = [telegram.to_bytes(), b'']

        self.server.incoming_telegram_subject = Subject[Telegram]()
        received_telegrams = []

        def on_next(data):
            received_telegrams.append(data)

        self.server.incoming_telegram_subject.subscribe(on_next)

        await self.server.handle_client_connection(mock_reader, mock_writer)

        mock_writer.get_extra_info.assert_called_with('peername')
        mock_reader.read.assert_called_with(Telegram.NB_BYTES)
        self.assertIn(telegram, received_telegrams)
        self.assertEqual(len(received_telegrams), 1)
        mock_writer.close.assert_called_once()
        mock_writer.wait_closed.assert_called_once()

    async def test_broadcast_telegram(self):
        telegram = Telegram()
        mock_writer_1 = AsyncMock()
        mock_writer_1.is_closing.return_value = False
        mock_writer_2 = AsyncMock()
        mock_writer_2.is_closing.return_value = False

        self.server._clients.add(mock_writer_1)
        self.server._clients.add(mock_writer_2)
        self.server._write_locks[mock_writer_1] = AsyncMock()
        self.server._write_locks[mock_writer_2] = AsyncMock()

        with patch.object(self.server, 'send_telegram_async', new_callable=AsyncMock) as mock_send_telegram:
            mock_send_telegram.return_value = True
            self.server.broadcast_telegram(telegram)

            mock_send_telegram.assert_any_call(mock_writer_1, telegram)
            mock_send_telegram.assert_any_call(mock_writer_2, telegram)

    async def test_send_telegram_async(self):
        telegram = Telegram()
        mock_writer = AsyncMock()
        mock_writer.get_extra_info.return_value = ('127.0.0.1', 1234)
        self.server._write_locks[mock_writer] = AsyncMock()

        with patch.object(mock_writer, 'is_closing', MagicMock(return_value=False)):
            result = await self.server.send_telegram_async(mock_writer, telegram)

        self.assertTrue(result)
        mock_writer.write.assert_called_once_with(telegram.to_bytes())
        mock_writer.drain.assert_called_once()

    async def test_client_to_server_communication(self):
        telegram = Telegram.create_from_bytes(
            b'\x020PLC155000123000123456789__2500030002ABC00FGH_____________________________________________________\x03'
        )

        server_task = asyncio.create_task(self.server.start_server())
        await asyncio.sleep(0.1)

        _, client_writer = await asyncio.open_connection('127.0.0.1', self.port)

        received_telegrams_by_server = []

        def on_next(data):
            received_telegrams_by_server.append(data)

        self.server.incoming_telegram_subject.subscribe(on_next)

        client_writer.write(telegram.to_bytes())
        await client_writer.drain()
        await asyncio.sleep(0.1)

        self.assertIn(telegram, received_telegrams_by_server)
        self.assertEqual(len(received_telegrams_by_server), 1)

        client_writer.close()
        await client_writer.wait_closed()
        await self.server.stop_server()
        server_task.cancel()

    async def test_server_to_client_communication(self):
        telegram_data = b'\x020HVIS55000123000987654321__2500030002ABC00FGH_____________________________________________________\x03'

        server_task = asyncio.create_task(self.server.start_server())
        await asyncio.sleep(0.1)

        client_reader, client_writer = await asyncio.open_connection('127.0.0.1', self.port)

        self.server.broadcast_telegram(Telegram.create_from_bytes(telegram_data))
        await asyncio.sleep(0.1)

        received_data = await client_reader.read(Telegram.NB_BYTES)

        self.assertEqual(received_data, telegram_data)

        client_writer.close()
        await client_writer.wait_closed()
        await self.server.stop_server()
        server_task.cancel()


if __name__ == '__main__':
    unittest.main()
