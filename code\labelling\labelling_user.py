import csv
import os
import sys
from typing import Literal, Optional

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import cv2 as cv
import numpy as np
import reactivex as rx
import reactivex.operators as ops
from heliovision.gui import Gui
from loguru import logger

from utilities import gamma_correct_image, get_all_images_inside_folder, resize_image


class UserLabellingSession:
    """
    This class represents a labelling session for a specific user.
    """

    _csv_header: list[str] = ['image_index', 'image_name', 'label']

    def __init__(
        self,
        gui: Gui,
        name: str,
        folder_path: str,
        image_extension: str = 'png',
        preload_range_images: int = 10,
        gamma_factor: float = 1.8,
        desired_gui_width_image_pixels: int = 1980,
    ) -> None:
        """
        Initialize a new user labelling session.

        Args:
            gui: The GUI instance to communicate with.
            name: The name of the user.
            folder_path: The path to the folder containing the images to label.
            image_extension: The extension of the images in the folder.
            preload_range_images: The amount of images to preload in the GUI.
            gamma_factor: The gamma factor to apply to the images.
            desired_gui_width_image_pixels: The desired width of the images in the GUI
        """
        self._gui: Gui = gui
        self._name: str = name
        self._folder_path: str = folder_path.replace('\\', '/')
        self._folder_name: str = os.path.basename(folder_path)
        self._image_extension: str = image_extension
        self._preload_range_images: int = preload_range_images
        self._gamma_factor: float = gamma_factor
        self._desired_gui_width_image_pixels: int = desired_gui_width_image_pixels

        if self._preload_range_images <= 0:
            raise ValueError(f'Preload range must be greater than 0, was: {self._preload_range_images!r}.')
        self._current_image_index: int = 1
        self._image_list: list[str] = get_all_images_inside_folder(
            path=self._folder_path, image_extension=self._image_extension
        )
        if not self._image_list:
            raise FileNotFoundError(f'No images found in folder: {self._folder_path!r}.')
        self._total_amount_of_images: int = len(self._image_list)
        logger.info(
            f'User({self._name!r}): Found {self._total_amount_of_images} valid images in folder: {self._folder_path!r}.'
        )
        self._labels: dict[int, str | None] = {i: None for i in range(1, self._total_amount_of_images + 1)}
        self._unlabelled_image_indices: set[int] = set(range(1, self._total_amount_of_images + 1))

        self._csv_file_path: str = os.path.join(self._folder_path, f'{self._name}.csv').replace('\\', '/')
        if os.path.exists(self._csv_file_path):
            self._load_existing_labels()
            logger.info(
                f'User({self._name!r}): Loaded {self._total_amount_of_images - len(self._unlabelled_image_indices)} existing labels from the csv file at: {self._csv_file_path!r}.'
            )

        self._image_indices_send_to_gui: set[int] = set()
        self._timestamp_last_push_save_button: float = 0

        # Save the labels to a CSV every 5 minutes
        self.automatic_saver = rx.timer(0, 300).pipe(ops.do_action(lambda x: self._save_labels_to_csv())).subscribe()

        # Load the first images
        self._update_preloaded_images()
        self._gui.broadcast_state()

    @property
    def name(self) -> str:
        return self._name

    def __json__(self) -> dict:
        return {
            'folder': self._folder_name,
            'totalAmountOfImages': self._total_amount_of_images,
            'lastSavedTimestamp': self._timestamp_last_push_save_button,  # tijdstip van handle_save push terug meegeven in deze
            'current': {
                'index': self._current_image_index,  # 1 indexed (user facing)
                'flight': 0,  # not used atm
                'format': '50',  # not used atm
                'crateImageName': self._image_list[self._current_image_index - 1],  # image name
                'imageId': f'{self._name}_{self._current_image_index}',  # live image name
                'label': self._labels[self._current_image_index],  # label of current image
            },
        }

    def _get_first_unlabelled_image_index(self) -> Optional[int]:
        return min(self._unlabelled_image_indices, default=None)

    def _increment_image_index(self) -> None:
        self._current_image_index += 1
        if self._current_image_index > len(self._image_list):
            self._current_image_index = 1

    def _decrement_image_index(self) -> None:
        self._current_image_index -= 1
        if self._current_image_index < 1:
            self._current_image_index = len(self._image_list)

    def _load_existing_labels(self) -> None:
        """
        Load existing labels from the csv file of this specific user.
        """
        logger.debug(f'Loading existing labels for user: {self._name!r} from the csv file at: {self._csv_file_path!r}.')
        with open(self._csv_file_path, mode='r') as csv_file:
            csv_reader = csv.DictReader(csv_file)

            if csv_reader.fieldnames != self._csv_header:
                logger.warning(f'CSV file at {self._csv_file_path!r} has an invalid header. Skipping it.')
                return

            for row in csv_reader:
                image_index: int = int(row['image_index'])
                image_name: str = row['image_name']
                label: str | None = row['label']

                if not label:
                    continue

                real_index: int = self._image_list.index(image_name) + 1
                if real_index != image_index:
                    logger.warning(
                        f'Image index mismatch for image: {image_name!r}. Expected: {real_index}, got: {image_index}. Some images may have been removed.'
                    )

                self._labels[real_index] = label
                self._unlabelled_image_indices.remove(real_index)
        logger.info(
            f'Successfully loaded existing labels for user: {self._name!r} from the csv file at: {self._csv_file_path!r}.'
        )

    def _save_labels_to_csv(self) -> None:
        """
        Save the current labels to the csv file for this specific user.
        Format of the csv file: ['image_index', 'image_name', 'label']
        """
        logger.debug(f'Saving labels for user: {self._name!r} to the csv file at: {self._csv_file_path!r}.')
        with open(self._csv_file_path, mode='w', newline='') as csv_file:
            csv_writer = csv.DictWriter(csv_file, fieldnames=self._csv_header)
            csv_writer.writeheader()

            for image_index, label in self._labels.items():
                csv_writer.writerow(
                    {'image_index': image_index, 'image_name': self._image_list[image_index - 1], 'label': label}
                )
        logger.info(f'Labels successfully saved to {self._csv_file_path}.')

    def _load_image(self, image_index: int) -> Optional[np.ndarray]:
        """
        Load an image from the folder for the given index.
        The index is 1 indexed. The index is applied to the image list, from which the image name is retrieved.

        Args:
            image_index: The index of the image to load. 1 indexed.

        Returns:
            The loaded image as a numpy array, or None if the image could not be loaded.
        """
        logger.debug(f'User({self._name!r}): Loading image for index: {image_index}.')
        if 1 <= image_index <= len(self._image_list):
            image_name: str = self._image_list[image_index - 1]
            image_path: str = os.path.join(self._folder_path, image_name)
            image: np.ndarray = cv.imread(image_path, cv.IMREAD_UNCHANGED)
            return image

        logger.error(f'User({self._name!r}): Invalid image index to load: {image_index}')
        return None

    def _process_image_for_gui(self, image: np.ndarray) -> np.ndarray:
        """
        Process an image for the GUI.
        The image is rotated 90 degrees clockwise, gamma corrected and resized.

        Args:
            image: The image to process.

        Returns:
            The processed image as a numpy array.
        """
        image = cv.rotate(image, cv.ROTATE_90_CLOCKWISE)
        image_gamma = gamma_correct_image(image=image, gamma=self._gamma_factor)
        new_image = resize_image(image=image_gamma, desired_width_pixels=self._desired_gui_width_image_pixels)
        return new_image

    def _load_and_send_image_to_gui(self, image_index: int) -> None:
        """
        Load an image for the given index and send it to the GUI.
        The index is 1 indexed. The index is applied to the image list, from which the image name is retrieved.

        Args:
            image_index: The index of the image to load and send.
        """
        logger.info(f'User({self._name!r}): Loading and sending image for index: {image_index} to the GUI.')
        if image_index in self._image_indices_send_to_gui:
            logger.warning(
                f'User({self._name!r}): Image for index {image_index} has already been sent to the GUI. Skipping this.'
            )
            return

        image = self._load_image(image_index)
        if image is None:
            logger.error(f'User({self._name!r}): Could not load image for index: {image_index}')
            return

        new_image = self._process_image_for_gui(image)
        self._gui.update_live_image(f'{self._name}_{image_index}', new_image, format='.jpg')
        self._gui.broadcast_state()
        self._image_indices_send_to_gui.add(image_index)
        logger.info(f'User({self._name!r}): Image for index {image_index} has been sent to the GUI.')

    def _update_preloaded_images(self) -> None:
        """
        Update the preloaded images in the GUI, based on the current image index.
        """
        logger.debug(f'User({self._name!r}): Updating preloaded images sent to the GUI.')
        forward_indices = [
            (self._current_image_index + i - 1) % self._total_amount_of_images + 1
            for i in range(1, self._preload_range_images + 1)
        ]
        backward_indices = [
            (self._current_image_index - i - 1) % self._total_amount_of_images + 1
            for i in range(1, self._preload_range_images + 1)
        ]

        desired_preload_indices = set(forward_indices + backward_indices)
        desired_preload_indices.add(self._current_image_index)

        image_indices_to_unload = self._image_indices_send_to_gui - desired_preload_indices
        image_indices_to_preload = desired_preload_indices - self._image_indices_send_to_gui

        for image_index in image_indices_to_preload:
            self._load_and_send_image_to_gui(image_index)

        for image_index in image_indices_to_unload:
            self._gui.clear_live_image(
                f'{self._name}_{image_index}',
            )
            self._image_indices_send_to_gui.remove(image_index)
            logger.info(f'User({self._name!r}): Unloaded image {image_index} from the GUI.')

    def handle_button(self, button: Literal['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'Run-off'] | None) -> None:
        """
        Handle a button press by the user.
        If the 'Clear' button is pressed on the GUI, we receive None and clear the label.
        Valid buttons are: 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'Run-off' and None.

        Args:
            button: The button that was pressed by the user. String.
        """
        logger.info(f'User({self._name!r}): Button {button!r} pressed by the user.')
        if button is None:
            logger.warning(
                f'User({self._name!r}): Label was cleared for image with index: {self._current_image_index}, received None as button press.'
            )
            self._labels[self._current_image_index] = None
            self._unlabelled_image_indices.add(self._current_image_index)
            self._gui.broadcast_state()
        else:
            self._labels[self._current_image_index] = button
            self._unlabelled_image_indices.discard(self._current_image_index)
            self.handle_switch_image('next')

    def handle_switch_image(
        self,
        action: Literal['previous', 'first', 'next', 'last', 'first-unlabelled', 'index'],
        index: int | None = None,
    ) -> None:
        """
        Handle a request to switch the image by the user.

        Args:
            action: The action to perform. One of: 'previous', 'first', 'next', 'last', 'first-unlabelled', 'index'.
            index: In case of action='index', the index to go to.
        """
        logger.info(f'User({self.name!r}): wants to switch image with action: {action}.')
        match action:
            case 'previous':
                self._decrement_image_index()
                self._gui.broadcast_state()
            case 'next':
                self._increment_image_index()
                self._gui.broadcast_state()
            case 'first':
                self._current_image_index = 1
            case 'last':
                self._current_image_index = len(self._image_list)
            case 'first-unlabelled':
                new_index = self._get_first_unlabelled_image_index()
                if not new_index:
                    logger.warning('No previous unlabelled image found.')
                    self._gui.show_message(
                        message=f'Gebruiker: {self._name!r}, alle afbeeldingen in deze folder zijn gelabeld.',
                        level='info',
                        time_out=10,
                    )
                    return
                self._current_image_index = new_index
            case 'index':
                if index is not None and 1 <= index <= self._total_amount_of_images:
                    self._current_image_index = index
                else:
                    logger.warning(f'Invalid index received for handle_switch_image: {index!r}')
            case _:
                logger.warning(f'Invalid action received for handle_switch_image: {action!r}')
                return

        self._update_preloaded_images()
        self._gui.broadcast_state()

    def handle_save_labelling(self, timestamp: float) -> None:
        """
        Handle a request to save the labelling of the user.
        """
        self._timestamp_last_push_save_button = timestamp  # Used for the spinner icon
        self._save_labels_to_csv()
        self._gui.broadcast_state()
        logger.info(f'User({self.name!r}): saved labelling to csv file.')

    def handle_stop_labelling(self) -> None:
        """
        Handle a request to stop the labelling session of the user.
        """
        logger.info(f'User({self.name!r}): wants to stop labelling.')
        self._save_labels_to_csv()
        if self.automatic_saver:
            self.automatic_saver.dispose()
        self.automatic_saver = None
        self._gui.broadcast_state()
        logger.info(f'User({self.name!r}): stopped labelling.')
