# Van Asseldonk - Mushrooms

## Description

Also see the [spec doc](https://docs.google.com/document/d/1cqGPUPcbh-bgya1ln6-bLIvoLt_SJTE7/)


We have to classify the mushroom quality on 3 different setups:
Vosdeel🦊, Mutshoek🎅, and Broekveld👖.

Vosdeel and Mutshoek will use TCP/IP communication with Frontmatec.
Broekveld is different.

## IPCs
IP at vosdeel:`**************`
IP at mutshoek:`************`

## PLCs
We listen on port: `5509`
IP at vosdeel:`**************`
IP at mutshoek:`************`

## Controllinos
IP at Vosdeel: `**************`
IP at Mutshoek: `*************`

## Services
The main script (helio_main_SITENAME) should be set up to start automatically on startup:  
```bash
sudo ln /home/<USER>/Documents/project/scripts/systemd/helio_main_SITENAME.service /etc/systemd/system/helio_main_SITENAME.service
sudo systemctl enable helio_main_SITENAME
```

You can manually start, stop, restart the services via:
To stop manually: `sudo systemctl stop helio_main_SITENAME`
To start manually: `sudo systemctl start helio_main_SITENAME`  
To restart manually: `sudo systemctl restart helio_main_SITENAME`  
To look at service logs: `journalctl -b -u helio_main_SITENAME`

For the labelling tools, the naming convention is as follows:
- `helio_labelling_backend_SITENAME.service`
- `helio_labelling_gui_SITENAME.service`
The backend service waits for the label gui to be started before starting itself.

## Double Checking Labels
To list all label files, you can use the following command:
```bash
find /mnt/sda/images/ -type f -name "*.csv"
```

## Upload labels and images to wasabi
Use the `upload_labels.py` script to upload all csv files and the corresponding images to wasabi.

## Dataflow

```mermaid
graph TB    
    TCPServer -->|"Incoming Telegram"| TelegramHandler
    TelegramHandler -->|"Outgoing Telegram"| TCPServer
    WatchdogHandler -->|"Outgoing Telegram"| TCPServer
    
    TelegramHandler -->|"Watchdog Telegram"| WatchdogHandler
    TelegramHandler -->|"Crate classification Feedback operator"| Synchronizer
    TelegramHandler -->|"New Crate"| Synchronizer
    Main -->|"New Classification for crate"| TelegramHandler
    
    Synchronizer ~~~ Camera -->|"New Image"| Synchronizer
    Camera ~~~ Synchronizer
    Synchronizer -->|"Image & Data"| Classifier
    Main ~~~ Classifier -->|"Classification"| Main
    Classifier ~~~ Main
```
