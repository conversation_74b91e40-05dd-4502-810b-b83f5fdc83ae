STX = b'\x02'
ETX = b'\x03'

_REPEAT_INDEX = 1
_SENDER_ID_INDEX = slice(2, 6)
_CMD_INDEX = slice(6, 8)
_TELEGRAM_NR_INDEX = slice(8, 14)

_TRACE_ID_INDEX = slice(14, 26)

_NEW_CRATE_FILL_STATION_INDEX = slice(26, 30)
_NEW_CRATE_CELL_ID_INDEX = slice(30, 34)
_NEW_CRATE_FLIGHT_ID_INDEX = slice(34, 38)
_NEW_CRATE_POSSIBLE_CLASSES_INDEX = slice(38, 99)

_AI_INFERENCE_CLASSIFICATION_INDEX = slice(26, 99)

_CLASSIFICATION_FEEDBACK_BUTTON_PRESSED_INDEX = slice(26, 99)


class Telegram:
    NB_BYTES = 100
    __DEFAULT_BYTES = STX + b'0HVIS00000000' + b'_' * (NB_BYTES - 15) + ET<PERSON>
    assert len(__DEFAULT_BYTES) == NB_BYTES

    def __init__(self) -> None:
        """
        Create a new telegram with default values.
        """
        self._bytes = bytearray(Telegram.__DEFAULT_BYTES)

    def to_bytes(self) -> bytes:
        """
        Convert the current telegram to bytes.

        Returns:
            bytes: The telegram as bytes.
        """
        return bytes(self._bytes)

    def _set_telegram_part(self, index: int | slice, value: bytes, right_fill: bool = False) -> None:
        """
        Set a part of the telegram to a new value.

        Args:
            index: The index or slice of the telegram to set. Should be an integer or a slice.
            value: The new value to set, as bytes.
            right_fill: Whether to right-fill the value with "b'_'" if it is too short. Boolean.

        Raises:
            ValueError: If the index is invalid, the value is too long or too short, or if the indices are not continuous.
        """
        if isinstance(index, int):
            start, stop, step = index, index + 1, 1
        else:
            start, stop, step = index.indices(Telegram.NB_BYTES)

        target_size = stop - start
        if right_fill and len(value) < target_size:
            value += b'_' * (target_size - len(value))

        if len(value) != target_size:
            raise ValueError(f'Invalid value length. Must be {target_size}, was {len(value)}.')

        if step != 1:
            raise ValueError('Telegram indices must be continuous.')

        if start > stop:
            raise ValueError('Invalid slice. Start must be less than stop.')

        if stop > Telegram.NB_BYTES:
            raise ValueError(f'Invalid slice. Stop must be less than or equal to {Telegram.NB_BYTES}.')

        if stop == Telegram.NB_BYTES and value[-1] != ETX:
            raise ValueError("Invalid value. Can't destroy ETX.")

        if start < 0:
            raise ValueError('Invalid slice. Start must be greater than or equal to 0.')

        if start == 0 and value[0] != STX:
            raise ValueError("Invalid value. Can't destroy STX.")

        self._bytes[start:stop] = value
        assert len(self._bytes) == Telegram.NB_BYTES, f'Invalid telegram length: {len(self._bytes)}.'

    @classmethod
    def create_from_bytes(cls, data: bytes) -> 'Telegram':
        """
        Create a new telegram from bytes.

        Args:
            data: The bytes to create the telegram from. Must be exactly 100 bytes long.

        Returns:
            Telegram: The new telegram object.

        Raises:
            ValueError: If the data is not 100 bytes long, or if it does not start with STX or end with ETX.
        """
        if len(data) != Telegram.NB_BYTES:
            raise ValueError(f'Invalid data length. Must be {Telegram.NB_BYTES}, was {len(data)}.')

        if data[0:1] != STX or data[-1:] != ETX:
            raise ValueError(f'Invalid data. Must start with STX and end with ETX. Were: {data[0]!r}, {data[-1]!r}.')

        telegram = cls()
        telegram._bytes[:] = data
        return telegram

    def __eq__(self, other) -> bool:
        """
        Compare two telegrams for equality.
        Only true if the bytes are equal.
        """
        if not isinstance(other, Telegram):
            return False
        return self._bytes == other._bytes

    def __hash__(self) -> int:
        """
        Create a hash value for the telegram.
        """
        return hash(self._bytes)

    def __str__(self) -> str:
        """
        Convert the telegram to a string.
        """
        return repr(self.to_bytes())

    @property
    def repeat_flag(self) -> bool:
        """
        Get the repeat flag of the telegram. True if the byte is set to 1.

        Returns:
            bool: True if the telegram is a repeat, False otherwise
        """
        return self._bytes[_REPEAT_INDEX : _REPEAT_INDEX + 1] == b'1'

    @repeat_flag.setter
    def repeat_flag(self, value: bool) -> None:
        """
        Set the repeat flag of the telegram. Set to "b'1'" if True, "b'0'" if False.

        Args:
            value: The new value of the repeat flag. Boolean.
        """
        if value:
            repeat_val = b'1'
        else:
            repeat_val = b'0'

        self._set_telegram_part(index=_REPEAT_INDEX, value=repeat_val, right_fill=False)

    @property
    def sender_id(self) -> str:
        """
        Get the sender ID of the telegram.

        Returns:
            str: The sender ID as a 4-character string.
        """
        return self._bytes[_SENDER_ID_INDEX].decode('ascii')

    @sender_id.setter
    def sender_id(self, value: str) -> None:
        """
        Set the sender ID of the telegram.

        Args:
            value: The new sender ID as a 4-character string.
        """
        self._set_telegram_part(index=_SENDER_ID_INDEX, value=value.encode('ascii'), right_fill=False)

    @property
    def cmd(self) -> int:
        """
        Get the command of the telegram.

        Returns:
            int: The command as an integer.
        """
        return int(self._bytes[_CMD_INDEX].decode('ascii'))

    @cmd.setter
    def cmd(self, value: int) -> None:
        """
        Set the command of the telegram.

        Args:
            value: The new command as an integer. Must be one of 1, 2, 55, 56 or 57.
        Raises:
            ValueError: If the command is not one of 1, 2, 55, 56 or 57.
        """
        if value not in {1, 2, 55, 56, 57}:
            raise ValueError(f'Invalid command: {value}. Must be one of 1,2,55,56 or 57.')
        value_str = str(value).zfill(2)
        self._set_telegram_part(index=_CMD_INDEX, value=value_str.encode('ascii'), right_fill=False)

    @property
    def telegram_nr(self) -> str:
        """
        Get the telegram number of the telegram.

        Returns:
            str: The telegram number as a 6-character string.
        """
        return self._bytes[_TELEGRAM_NR_INDEX].decode('ascii')

    @telegram_nr.setter
    def telegram_nr(self, value: str) -> None:
        """
        Set the telegram number of the telegram.

        Args:
            value: The new telegram number as a 6-character string.
        Raises:
            ValueError: If the telegram number is not between 1 and 999_999.
        """
        value_int = int(value)
        if not 1 <= value_int <= 999_999:
            raise ValueError(f'Invalid telegram number: {value_int}. Must be between 1 and 999_999.')
        if len(value) != 6:
            value = str(value_int).zfill(6)
        self._set_telegram_part(index=_TELEGRAM_NR_INDEX, value=value.encode('ascii'), right_fill=False)

    @property
    def trace_id(self) -> str:
        """
        Get the trace ID of the telegram.

        Returns:
            str: The trace ID as a 12-character string.
        """
        return self._bytes[_TRACE_ID_INDEX].decode('ascii').rstrip('_')

    @trace_id.setter
    def trace_id(self, value: str):
        """
        Set the trace ID of the telegram.

        Args:
            value: The new trace ID as a 12-character string.
        Raises:
            ValueError: If the trace ID is not 12 characters long.
        """
        if len(value) != 12:
            raise ValueError(f'Invalid trace ID: {value!r}. Must be 12 characters long.')
        self._set_telegram_part(index=_TRACE_ID_INDEX, value=value.encode('ascii'), right_fill=False)

    @property
    def new_crate_fill_station(self) -> str:
        """
        Get the fill station of the new crate telegram.

        Returns:
            str: The fill station.
        """
        return self._bytes[_NEW_CRATE_FILL_STATION_INDEX].decode('ascii').lstrip('_')

    @property
    def new_crate_cell_id(self) -> str:
        """
        Get the cell ID of the new crate telegram.

        Returns:
            str: The cell ID.
        """
        return self._bytes[_NEW_CRATE_CELL_ID_INDEX].decode('ascii').rstrip('_').lstrip('0')

    @property
    def new_crate_flight_id(self) -> str:
        """
        Get the flight ID of the new crate telegram.

        Returns:
            str: The flight ID.
        """
        return self._bytes[_NEW_CRATE_FLIGHT_ID_INDEX].decode('ascii').rstrip('_').lstrip('0')

    @property
    def new_crate_possible_classes(self) -> str:
        """
        Get the possible classes of the new crate telegram.

        Returns:
            str: The possible classes.
        """
        return self._bytes[_NEW_CRATE_POSSIBLE_CLASSES_INDEX].decode('ascii').rstrip('_')

    @property
    def ai_inference_classification(self) -> str:
        """
        Get the AI inference classification of the telegram.

        Returns:
            str: The classification by the AI system as a string
        """
        return self._bytes[_AI_INFERENCE_CLASSIFICATION_INDEX].decode('ascii').rstrip('_').lstrip('_')

    @ai_inference_classification.setter
    def ai_inference_classification(self, value: str) -> None:
        """
        Set the AI inference classification of the telegram.

        Args:
            value: The new classification by the AI system as a string.

        Raises:
            ValueError: If the classification is not one of A, B, C, D, E, F, G or H.
        """
        if value not in {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'}:
            raise ValueError(f'Invalid classification: {value!r}. Must be one of A, B, C, D, E, F, G or H.')
        self._set_telegram_part(
            index=_AI_INFERENCE_CLASSIFICATION_INDEX, value=b'___' + value.encode('ascii'), right_fill=True
        )

    @property
    def classification_feedback_button_pressed(self) -> str:
        """
        Get the button pressed value of the classification feedback telegram.

        Returns:
            str: The button pressed value as a string.
        """
        return self._bytes[_CLASSIFICATION_FEEDBACK_BUTTON_PRESSED_INDEX].decode('ascii').rstrip('_').lstrip('_')
